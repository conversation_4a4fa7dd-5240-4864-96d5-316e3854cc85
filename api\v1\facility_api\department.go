package facility_api

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/resource"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

type DepartmentController struct {
	v1.CommonController
}

func NewDepartmentController() DepartmentController {
	return DepartmentController{}
}

// @Tags Department
// @Summary 新增部門
// @Description
// @Router /v1/facility/departments/actions/create [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.DepartmentCreateReq true "parameter"
// @Success 200 {object} services.DepartmentCreateResp "Success"
func (con DepartmentController) Create(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DepartmentCreateReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		tx := db.Begin()
		resp, err := services.DepartmentService.Create(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Department
// @Summary 獲取部門列表
// @Description
// @Router /v1/facility/departments [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.DepartmentListReq true "parameter"
// @Param pageIndex query int32 false "頁數"
// @Param pageSize query int32 false "每頁條目數"
// @Success 200 {object} []services.DepartmentListResp "Success"
func (con DepartmentController) List(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DepartmentListReq
	var pageSet xresp.PageSet
	_ = c.ShouldBindQuery(&pageSet)
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		resp, err := services.DepartmentService.List(db, req, &pageSet)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponseWithPage(resp, pageSet)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Department
// @Summary 搜索部門
// @Description
// @Router /v1/facility/departments/actions/search [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.DepartmentSearchReq true "parameter"
// @Success 200 {object} []services.DepartmentSearchResp "Success"
func (con DepartmentController) Search(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DepartmentSearchReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.DepartmentService.Search(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Department
// @Summary 修改部門
// @Description
// @Router /v1/facility/departments/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.DepartmentEditReq true "parameter"
// @Success 200 "Success"
func (con DepartmentController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DepartmentEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DepartmentService.CheckIdExist(db, &model.Department{}, req.DepartmentId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.DepartmentService.Edit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Department
// @Summary 查询部門
// @Description
// @Router /v1/facility/departments/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json query services.DepartmentInquireReq true "parameter"
// @Success 200 {object} services.DepartmentInquireResp "Success"
func (con DepartmentController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DepartmentInquireReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		resp, err := services.DepartmentService.Inquire(db, req)
		if xgorm.IsNotFoundErr(err) {
			nc.BadRequestResponse(err)
			return
		}
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Department
// @Summary 删除部門
// @Description
// @Router /v1/facility/departments/actions/delete [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.DepartmentDeleteReq true "parameter"
// @Success 200 "Success"
func (con DepartmentController) Delete(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.DepartmentDeleteReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		if !con.CheckCanAccess(nc, db, v1.ReqCommonCheck{FacilityId: req.FacilityId}) {
			nc.NoPermissionResponse(resource.ForbiddenMsg)
			return
		}
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.DepartmentService.CheckIdExist(db, &model.Department{}, req.DepartmentId, req.FacilityId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.DepartmentService.CheckIsUsed(db, req.DepartmentId, req.FacilityId)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		tx := db.Begin()
		err = services.DepartmentService.Delete(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}
