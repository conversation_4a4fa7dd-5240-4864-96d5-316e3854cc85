package professional_api

type ProfessionalReferenceFormDeclineController struct{}

func NewProfessionalReferenceFormDeclineController() ProfessionalReferenceFormDeclineController {
	return ProfessionalReferenceFormDeclineController{}
}

// @Tags Professional Reference Form
// @Summary 推薦人拒絕填寫推薦表單
// @Description 推薦人拒絕填寫推薦表單
// @Router /v1/professional/reference-forms/actions/decline [POST]
// @Accept json
// @Produce json
// @Param json body services.ProfessionalReferenceFormDeclineReq true "parameter"
// @Success 200 {object} services.ProfessionalReferenceFormDeclineResp "Success"
//func (con ProfessionalReferenceFormDeclineController) Decline(c *gin.Context) {
//	nc := xapp.NGinCtx{C: c}
//	var req services.ProfessionalReferenceFormDeclineReq
//	if err := c.ShouldBindJSON(&req); err == nil {
//		db := xgorm.DB.WithContext(c)
//
//		tx := db.Begin()
//		var resp services.ProfessionalReferenceFormDeclineResp
//		resp, err = services.ProfessionalReferenceFormService.Decline(tx, req)
//		if err != nil {
//			tx.Rollback()
//			nc.ErrorResponse(req, err)
//			return
//		}
//		tx.Commit()
//
//		nc.OKResponse(resp)
//	} else {
//		nc.BadRequestResponse(err)
//	}
//}
