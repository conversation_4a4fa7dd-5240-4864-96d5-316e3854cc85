package job

//const (
//	CronCheckDocumentExpiry                    = "cron_check_document_expiry"     // 檢查文件到期
//	CheckDocumentExpiryMaxProcessRecordsPerRun = 100                              // 每次處理的最大記錄數
//	CheckDocumentExpiryLockTimeoutSeconds      = 50                               // 鎖定超時時間（秒）
//)
//
//type DocumentExpiryInfo struct {
//	FileId       uint64
//	UserId       uint64
//	DocumentType string
//	ExpiryDate   time.Time
//	FileName     string
//}
//
//// 檢查文件到期定時任務 - 每天執行
//func jobCheckDocumentExpiry() {
//	traceId := uuid.NewV4().String()
//	ctx := context.Background()
//	ctx = context.WithValue(ctx, "traceId", traceId)
//
//	logger := log.WithField("traceId", traceId).With<PERSON>ield("task", CronCheckDocumentExpiry)
//
//	db := xgorm.DB.WithContext(ctx)
//	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckDocumentExpiry)
//	if err != nil {
//		logger.Errorf("[CRON] fail to check document expiry task: %v", err)
//		return
//	}
//	if !run {
//		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckDocumentExpiry)
//		return
//	}
//
//	nowTime := time.Now().UTC().Truncate(time.Second)
//	thirtyDaysLater := nowTime.AddDate(0, 0, 30)
//
//	// 檢查即將到期的文件（30天內到期）
//	checkExpiringDocuments(db, nowTime, thirtyDaysLater, logger)
//
//	logger.Info("document expiry check completed")
//}
//
//// 檢查即將到期的文件
//func checkExpiringDocuments(db *gorm.DB, nowTime, thirtyDaysLater time.Time, logger *log.Entry) {
//	// TODO: 某個文件距離日到期日不足30日 - 通知Professional
//
//	var expiringDocuments []DocumentExpiryInfo
//
//	// 查詢即將到期的專業人士文件
//	builder := db.Table("professional_file AS pf").
//		Select([]string{
//			"pf.id AS file_id",
//			"p.user_id",
//			"pf.file_code AS document_type",
//			"pf.expiry_date",
//			"pf.file_name",
//		}).
//		Joins("JOIN professional AS p ON p.id = pf.professional_id").
//		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_file_id = pf.id").
//		Where("p.data_type = ?", model.ProfessionalDataTypeApproved). // 只檢查已審核通過的專業人士
//		Where("pf.expiry_date IS NOT NULL").
//		Where("pf.expiry_date BETWEEN ? AND ?", nowTime, thirtyDaysLater).
//		Where("pf.status = ?", model.ProfessionalFileStatusApproved). // 只檢查已審核通過的文件
//		Group("pf.id, p.user_id, pf.file_code, pf.expiry_date, pf.file_name").
//		Limit(CheckDocumentExpiryMaxProcessRecordsPerRun)
//
//	if err := builder.Scan(&expiringDocuments).Error; err != nil {
//		logger.Errorf("[CRON] fail to get expiring documents: %v", err)
//		return
//	}
//
//	for _, doc := range expiringDocuments {
//		// TODO: 調用通知服務發送文件即將到期通知給Professional
//		logger.Infof("Should send document expiry notification to professional - FileId: %d, UserId: %d, DocumentType: %s, ExpiryDate: %s",
//			doc.FileId, doc.UserId, doc.DocumentType, doc.ExpiryDate.Format("2006-01-02"))
//	}
//}
