package job

import (
	"context"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const (
	CronCheckFacilityJobConfirmations                    = "cron_check_facility_job_confirmations" // 檢查機構工作確認
	CheckFacilityJobConfirmationsMaxProcessRecordsPerRun = 100                                     // 每次處理的最大記錄數
	CheckFacilityJobConfirmationsLockTimeoutSeconds      = 50                                      // 鎖定超時時間（秒）
)

// 檢查機構工作確認定時任務 - 每分鐘執行
func jobCheckFacilityJobConfirmations() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckFacilityJobConfirmations)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckFacilityJobConfirmations)
	if err != nil {
		logger.Errorf("[CRON] fail to check facility job confirmations task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckFacilityJobConfirmations)
		return
	}

	// TODO: 這個檔案的功能已經移動到 cron_check_job_reminders.go
	// 保留這個函數以防有其他地方調用，但實際功能已經移動

	logger.Info("facility job confirmations check completed")
}
