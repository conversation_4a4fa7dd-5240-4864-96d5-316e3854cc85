package job

import (
	"context"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const (
	CronCheckJobCompletion                    = "cron_check_job_completion" // 檢查工作完成
	CheckJobCompletionMaxProcessRecordsPerRun = 100                         // 每次處理的最大記錄數
	CheckJobCompletionLockTimeoutSeconds      = 50                          // 鎖定超時時間（秒）
)

// 檢查工作完成定時任務 - 每分鐘執行
func jobCheckJobCompletion() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobCompletion)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckJobCompletion)
	if err != nil {
		logger.Errorf("[CRON] fail to check job completion task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckJobCompletion)
		return
	}

	// TODO: 這個檔案的功能已經移動到 cron_check_job_reminders.go
	// 保留這個函數以防有其他地方調用，但實際功能已經移動

	logger.Info("job completion check completed")
}
