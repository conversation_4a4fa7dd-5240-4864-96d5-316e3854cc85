package job

import (
	"time"

	"github.com/Norray/medic-crew/model"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 工作自動取消定時任務
type CronJobAutoCancel struct {
	db *gorm.DB
}

func NewCronJobAutoCancel(db *gorm.DB) *CronJobAutoCancel {
	return &CronJobAutoCancel{
		db: db,
	}
}

// 檢查並自動取消無申請的工作
func (c *CronJobAutoCancel) CheckAndCancelJobsWithoutApplications() {
	log.Info("開始檢查並自動取消無申請的工作")

	// 計算1小時後的時間
	now := time.Now().UTC()
	oneHourLater := now.Add(1 * time.Hour)

	// TODO: 距離工作開始1小時前還未有人申請工作，工作自動取消 - 通知Facility
	// 查詢1小時後開始且無申請的工作
	var jobs []model.Job
	err := c.db.Where("begin_time <= ? AND status = ?", oneHourLater, model.JobStatusPublish).
		Find(&jobs).Error
	if err != nil {
		log.Errorf("查詢需要自動取消的工作失敗: %v", err)
		return
	}

	cancelledCount := 0
	for _, job := range jobs {
		// 檢查是否有申請
		var applicationCount int64
		c.db.Model(&model.JobApplication{}).Where("job_id = ?", job.Id).Count(&applicationCount)

		if applicationCount == 0 {
			// 開始事務
			tx := c.db.Begin()

			// 自動取消工作
			job.Status = model.JobStatusCancel
			job.CancelReason = "系統自動取消：工作開始前1小時無人申請"
			job.CancelTime = &now
			job.UpdateTime = &now

			if err := tx.Save(&job).Error; err != nil {
				tx.Rollback()
				log.Errorf("自動取消工作失敗: %v", err)
				continue
			}

			// TODO: 更新工作班次狀態 - 需要確認JobShift模型是否存在status字段
			// 暫時跳過班次狀態更新，因為JobShift模型可能沒有status字段

			// 提交事務
			if err := tx.Commit().Error; err != nil {
				log.Errorf("提交事務失敗: %v", err)
				continue
			}

			// TODO: 發送自動取消通知給機構
			// 需要實現相應的通知服務方法
			// 通知內容：您的工作因為開始前1小時無人申請已被系統自動取消
			log.Infof("工作已自動取消: 工作ID=%d, 機構ID=%d, 開始時間=%s",
				job.Id, job.FacilityId, job.BeginTime.Format("2006-01-02 15:04:05"))

			cancelledCount++
		}
	}

	log.Infof("工作自動取消檢查完成，共取消 %d 個工作", cancelledCount)
}

// 檢查無確認候選人的工作
func (c *CronJobAutoCancel) CheckJobsWithoutConfirmedCandidate() {
	log.Info("開始檢查無確認候選人的工作")

	// 計算24小時後的時間
	now := time.Now().UTC()
	twentyFourHoursLater := now.Add(24 * time.Hour)

	// TODO: 距離工作開始24小時前還未確認好人選 - 通知Facility
	// 查詢24小時後開始但還未確認候選人的工作
	var jobs []model.Job
	err := c.db.Where("begin_time <= ? AND status = ?", twentyFourHoursLater, model.JobStatusPublish).
		Find(&jobs).Error
	if err != nil {
		log.Errorf("查詢無確認候選人的工作失敗: %v", err)
		return
	}

	for _, job := range jobs {
		// 檢查是否有已接受的申請
		var acceptedApplicationCount int64
		c.db.Model(&model.JobApplication{}).
			Where("job_id = ? AND status = ?", job.Id, model.JobApplicationStatusAccept).
			Count(&acceptedApplicationCount)

		if acceptedApplicationCount == 0 {
			// 檢查是否有待處理的申請
			var pendingApplicationCount int64
			c.db.Model(&model.JobApplication{}).
				Where("job_id = ? AND status = ?", job.Id, model.JobApplicationStatusApply).
				Count(&pendingApplicationCount)

			if pendingApplicationCount > 0 {
				// 獲取機構資料信息
				var facilityProfile model.FacilityProfile
				if err := c.db.Where("facility_id = ? AND data_type = ?",
					job.FacilityId, model.FacilityProfileDataTypeApproved).First(&facilityProfile).Error; err != nil {
					log.Errorf("獲取機構資料信息失敗: %v", err)
					continue
				}

				// TODO: 發送未確認候選人提醒通知給機構
				// 需要實現相應的通知服務方法
				// 通知內容：距離工作開始僅剩24小時，請儘快確認候選人
				log.Infof("需要發送未確認候選人提醒: 工作ID=%d, 機構ID=%d, 開始時間=%s, 待處理申請數=%d",
					job.Id, job.FacilityId, job.BeginTime.Format("2006-01-02 15:04:05"), pendingApplicationCount)
			}
		}
	}

	log.Info("無確認候選人工作檢查完成")
}
