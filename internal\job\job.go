package job

import (
	"github.com/Norray/xrocket/xcron"
	log "github.com/sirupsen/logrus"
)

func ArrangeCronJob() {
	// 每分鐘的第1秒時更新 專業人士搜索職位設定
	var err error
	//if err = xcron.NewJob("1 * * * * ?", xcron.MakeCronJob(CronCleanProfessionalJobAvailability, cleanProfessionalJobAvailability)); err != nil {
	//	log.Fatalf("cron can not init job %s: %v", CronCleanProfessionalJobAvailability, err)
	//}
	//// 每天0點1秒時檢查是有需要發佈的工作
	//if err = xcron.NewJob("1 0 0 * * ?", xcron.MakeCronJob(CronJobSchedulePublishTask, jobSchedulePublishTask)); err != nil {
	//	log.Fatalf("cron can not init job %s: %v", CronJobSchedulePublishTask, err)
	//}
	//// 每分鐘檢查有無機構發出邀請超過12小時未相應的記錄，並取消邀請
	//if err = xcron.NewJob("1 * * * * ?", xcron.MakeCronJob(CronCheckFacilityInvitationTimeout, jobCheckFacilityInvitationTimeout)); err != nil {
	//	log.Fatalf("cron can not init job %s: %v", CronCheckFacilityInvitationTimeout, err)
	//}
	//// 每分鐘檢查是否有需要生成發票的工作
	//if err = xcron.NewJob("30 * * * * *", xcron.MakeCronJob(CronCheckJobForInvoice, CheckJobForInvoice)); err != nil {
	//	log.Fatalf("cron can not init job %s: %v", CronCheckJobForInvoice, err)
	//}
	//// 每分鐘檢查是否有需要發送預付發票的工作
	//if err = xcron.NewJob("45 * * * * *", xcron.MakeCronJob(CronCheckSendPayUpfrontInvoice, CheckSendPayUpfrontInvoice)); err != nil {
	//	log.Fatalf("cron can not init job %s: %v", CronCheckSendPayUpfrontInvoice, err)
	//}
	// 每分鐘檢查是否有需要發送工作提醒
	if err = xcron.NewJob("1 * * * * ?", xcron.MakeCronJob(CronCheckJobReminders, jobCheckJobReminders)); err != nil {
		log.Fatalf("cron can not init job %s: %v", CronCheckJobReminders, err)
	}
	log.Info("init job")
}

func StopCronJob() {
	log.Info("stop job")
	xcron.Close()
}
