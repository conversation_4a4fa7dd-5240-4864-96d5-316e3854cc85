package task

import (
	"context"
	"encoding/json"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
)

const GenerateCompensationConfirmationNoteTask = "generate_compensation_confirmation_note_task" // rabbitmq

// 執行隊列 - 生成賠付賬單草稿
func GenerateCompensationConfirmationNote(taskJson string) (context.Context, error) {
	var err error
	ctx := context.Background()
	traceId := uuid.NewV4().String()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", GenerateCompensationConfirmationNoteTask)

	taskData := services.GenerateCompensationReq{}
	err = json.Unmarshal([]byte(taskJson), &taskData)
	if err != nil {
		logger.Errorf("fail to unmarshal task: %v, taskJson: %s", err, taskJson)
		return ctx, err
	}
	logger = logger.
		WithField("JobShiftId", taskData.JobShiftId).
		WithField("JobApplicationId", taskData.JobApplicationId).
		WithField("CompensationHours", taskData.CompensationHours.String())

	logger.Info("Start to create compensation confirmation note")

	// 獲取數據庫連接
	db := xgorm.DB.WithContext(ctx)

	var jobApplication model.JobApplication
	if err = db.First(&jobApplication, taskData.JobApplicationId).Error; err != nil {
		logger.Errorf("fail to get job application: %v", err)
		return ctx, err
	}

	var jobShift model.JobShift
	if err = db.First(&jobShift, taskData.JobShiftId).Error; err != nil {
		logger.Errorf("fail to get job application: %v", err)
		return ctx, err
	}

	// 開啟事務
	tx := db.Begin()

	// 嘗試生成賬單
	err = services.ConfirmationNoteService.CreateCompensationByJobShift(tx, jobApplication, jobShift, taskData.CompensationHours, taskData.Particular)
	if err != nil {
		tx.Rollback()
		logger.Errorf("fail to compensation confirmation note: %v", err)
		return ctx, err
	}

	// 提交事務
	if err = tx.Commit().Error; err != nil {
		logger.Errorf("fail to commit transaction: %v", err)
		return ctx, err
	}

	logger.Info("create compensation confirmation note task completed")
	return ctx, nil
}
