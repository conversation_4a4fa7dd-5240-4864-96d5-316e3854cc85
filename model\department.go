package model

import "github.com/Norray/xrocket/xmodel"

const (
	DepartmentStatusEnable  = "ENABLE"
	DepartmentStatusDisable = "DISABLE"
)

// 部門
type Department struct {
	Id                  uint64 `json:"id" gorm:"primary_key"`
	FacilityId          uint64 `json:"facilityId" gorm:"type:bigint;not null;index:facility_idx"`                           // 機構Id
	ServiceLocationId   uint64 `json:"serviceLocationId" gorm:"type:bigint;not null;index:service_location_idx"`            // 服務地點ID
	Name                string `json:"name" gorm:"type:varchar(255);not null"`                                              // 部門名稱
	Status              string `json:"status" gorm:"type:varchar(16);not null;index:status_idx"`                            // 狀態 ENABLE=啟用 DISABLE=停用
	AccessAllDepartment string `json:"accessAllDepartment" gorm:"type:varchar(1);not null;index:access_all_department_idx"` // 訪問所有部門數據 Y=是 N=否
	xmodel.Model
}

func (Department) TableName() string {
	return "department"
}

func (Department) SwaggerDescription() string {
	return "部門"
}
