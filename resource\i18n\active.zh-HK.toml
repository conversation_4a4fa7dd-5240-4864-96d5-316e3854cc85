[""]
hash = "sha1-a35cb1b709b2dbb7494d4a4d12214f207ae40898"
other = "Unable to sign in this account."

["alerter.google_oauth.can_not_found_state"]
hash = "sha1-e82eeb32000a38045249281826d6e692710c55c3"
other = "Authorization timed out, please reauthorize."

["alerter.job.already_apply"]
hash = "sha1-b2f417666122a13a475ca0a4f3bb7945d481cacd"
other = "You have already applied for this job."

["alerter.job.expired"]
hash = "sha1-14e51e1b5266c1da64e2868e73a44eabb5816799"
other = "The job has expired."

["alerter.job.full"]
hash = "sha1-c52e8bf3cd304905b88ee03ebf12f8645692cdec"
other = "This job is full."

["alerter.job.not_published"]
hash = "sha1-d257d0e7083d22d8329808f3a214b948f7e9db8b"
other = "This job is not published."

["alerter.job.not_recruiting"]
hash = "sha1-d98c26d8c8aa525a89442a74f834154a3a6a8026"
other = "This job is not recruiting."

["alerter.refresh_token.can_not_found"]
hash = "sha1-e82eeb32000a38045249281826d6e692710c55c3"
other = "Authorization timed out, please reauthorize."

["casbin.authentication.forbidden"]
hash = "sha1-01f7ea20b40b40ee5b358d94d55f0c72558513ef"
other = "你沒有此權限。"

["change_password.failed"]
hash = "sha1-5adec5fd123736ad7dee4edd2380da92d521cad4"
other = "Old password is incorrect."

["checker.action.code.already_exists"]
hash = "sha1-410794012024910116b1b1393f0bdcf370bb5378"
other = "The action code is already exists. Please try another code."

["checker.action.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.agreement.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.allowance.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.benefit.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.casbin.id.does_not_exist"]
hash = "sha1-26f6988cfc328947ed8fbc2dcd553ecc6a19f6fe"
other = "Policy does not exist."

["checker.casbin.police.already_exists"]
hash = "sha1-8a485d71f7df172df7151746c1d27d98c351ca67"
other = "Police already exists."

["checker.commission.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.confirmation_note.cannot_modify"]
hash = "sha1-bf489ca01e3c3b3661a3f82a4f6854cd5952f04b"
other = "This confirmation note cannot be modified in current status."

["checker.confirmation_note.cannot_review"]
hash = "sha1-5c2d31986db0234049b8861a9b94f34116bada8f"
other = "This confirmation note cannot be reviewed in current status."

["checker.confirmation_note.cannot_submit"]
hash = "sha1-6944b6c42c08e2e9f3e076f3a69b204138bd6746"
other = "This confirmation note cannot be submitted in current status."

["checker.confirmation_note.cannot_void"]
hash = "sha1-dc9cf38c3786cec172da343dcdba3fb22fc97dda"
other = "This confirmation note cannot be voided in current status."

["checker.confirmation_note.gst.no_access"]
hash = "sha1-ccb7d875f020a2c2129d3f0bd15b003f1bfcc34e"
other = "No access for GST."

["checker.confirmation_note.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.confirmation_note.invalid_amount_summary"]
hash = "sha1-e69d55880f55a202779cfbfc2f429cc4321cfa97"
other = "Invalid amount summary."

["checker.confirmation_note.item.allowance_amount_invalid"]
hash = "sha1-1adf0ca32c1dbc67547d798ee8763406874509f6"
other = "Invalid allowance amount submitted."

["checker.confirmation_note.item.allowance_duplicate"]
hash = "sha1-ae2c6a844fec725794f9e5fcdb26ab81f0e7cb2f"
other = "Allowance duplicate."

["checker.confirmation_note.item.allowance_invalid"]
hash = "sha1-4984194bb5edd2ae838a7480153a1a1cc44486dd"
other = "Invalid allowance data submitted."

["checker.confirmation_note.item.allowance_not_found"]
hash = "sha1-ed77c63ce72510c7798420d6bbdd9bc41cbe3441"
other = "Allowance not found."

["checker.confirmation_note.item.allowance_super_amount_invalid"]
hash = "sha1-3c6910bfb8f187b6f555a50344657cc728e78920"
other = "Invalid allowance super amount submitted."

["checker.confirmation_note.item.invalid"]
hash = "sha1-781f08993e16253e2418f728dbcaccd83ff37047"
other = "Invalid item data submitted."

["checker.confirmation_note.not_creator"]
hash = "sha1-b42be3f3e2c5dbe4d60c4abbc4ebd123f3ca3195"
other = "You are not the creator of this confirmation note."

["checker.department.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.department.is_used"]
hash = "sha1-06c6bab047c7f45e4aad269772eef3f02854378f"
other = "This department is currently in use and cannot be deleted."

["checker.document.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.document_file.code.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.document_file.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.document_file.ids.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.facility_agreement.cannot_modify"]
hash = "sha1-7d26e266542cc3c5960fec260388c6330292f086"
other = "This agreement cannot be modified in current status."

["checker.facility_agreement.cannot_send_agreement_email"]
hash = "sha1-28703b2390a0b7b11116224aeb4ff95fbfc73990"
other = "This agreement cannot be sent to the facility."

["checker.facility_agreement.cannot_sign_agreement"]
hash = "sha1-06479641ab24c9c7731afca6498035f30fdef4c8"
other = "This agreement cannot be signed."

["checker.facility_agreement.has_unsigned_agreement"]
hash = "sha1-60eb6effdddbb96402b5ad6f8d1c2ae46931aa79"
other = "Please sign all pending agreements before proceeding."

["checker.facility_agreement.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.facility_agreement.invalid_time_range"]
hash = "sha1-d4c63d405763f36a3531381602ef0e0e24e12bc9"
other = "Begin time must be earlier than end time."

["checker.facility_agreement.no_valid_agreement"]
hash = "sha1-3fbf295c36eeb409aebf1f0b9aaa6f0c4dc31365"
other = "No valid agreement for the job time period."

["checker.facility_agreement.schedule_time_out_of_range"]
hash = "sha1-08c12101a4baa7135a84afee6459277cffcd0047"
other = "Schedule time must be within agreement time range."

["checker.facility_agreement.time_range_overlap"]
hash = "sha1-ad17f717f1759caef4c8f0d1ddfa4c37468b0589"
other = "The validity period of the agreement overlaps with that of an existing active agreement."

["checker.facility_blacklist.already_in_blacklist"]
hash = "sha1-e6ef338e2e1e7a19dbfa8f3c20fad27206fb41aa"
other = "The professional is already in the blacklist."

["checker.facility_blacklist.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.facility_file.code.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_file.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_file.ids.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_file.orientation_documentation.not_found"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.agreement_id.correct"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.already_init"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.can_not_approve"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.can_not_edit"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.can_not_submit_or_un_submit"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.facility_profile.information_incomplete"]
hash = "sha1-618bbe4b9f754d770a5c0b55ed37c911a49dd05e"
other = "Please check if all the required fields on the page have been completed."

["checker.facility_profile.public_liability_insurance.expired"]
hash = "sha1-4fee0c11b66897d0209cbc04962dae3e036a99a3"
other = "The Public Liability Insurance document has expired."

["checker.facility_specialisation.codes.invalid"]
hash = "sha1-39e5c3a154d28eee2539d19e036f0bb52d20ce06"
other = "One or more codes are invalid for the specified position."

["checker.facility_specialisation.codes.valid"]
hash = "sha1-03e05c86271085f5a03f294459db9c82ade4f5ae"
other = "All codes are valid for the specified position."

["checker.facility_specialisation.position.invalid"]
hash = "sha1-ea4a1924606747a5a60c183a9b418d53f5a12252"
other = "The specified position is not supported."

["checker.faq.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.faq_file.uuid.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.hourly_rate.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.invoice.admin.edit_sent_document"]
hash = "sha1-18d3cd200204d13418b867c704b58dca23c4b919"
other = "The document has been sent and cannot be edited."

["checker.invoice.invalid_amount_summary"]
hash = "sha1-e69d55880f55a202779cfbfc2f429cc4321cfa97"
other = "Invalid amount summary."

["checker.invoice.invalid_payment_status"]
hash = "sha1-8b1aacb57b9d47b9e970bf47bf3361924480cb65"
other = "Invalid payment status."

["checker.job.cannot_accept.time_has_job"]
hash = "sha1-861cf4da91a7efbc851cbfd8c5105c9638575c47"
other = "You already have another job scheduled during this time period."

["checker.job.cannot_delete"]
hash = "sha1-a6e91e6c368929ee75ca29d591b1ab776281b014"
other = "The job cannot be deleted."

["checker.job.cannot_delete.application"]
hash = "sha1-62dee6cab9ec5931afbd4305b9e37e69067ae493"
other = "The job has applicants, cannot be deleted."

["checker.job.cannot_invite.accept"]
hash = "sha1-fea9543badcd40afa19e8d077fed93ff4f793f5e"
other = "The professional has already accepted the invitation."

["checker.job.cannot_invite.application_cancel"]
hash = "sha1-1f9370a6337ce99eab753569082c0060fb5af937"
other = "You have already canceled the application."

["checker.job.cannot_invite.begin_time"]
hash = "sha1-89db9defe65bbdb58c085ab8356c324565d24b62"
other = "This job has already started."

["checker.job.cannot_invite.facility_cancel"]
hash = "sha1-1f9370a6337ce99eab753569082c0060fb5af937"
other = "You have already canceled the application."

["checker.job.cannot_invite.invite"]
hash = "sha1-eff6be349c449554ea5be5d103fb5ddf8a693355"
other = "You have already sent an invitation to this job."

["checker.job.cannot_invite.professional_cancel"]
hash = "sha1-e039abea1333ad43cc7cea11c614705a83f5fb71"
other = "The professional has already canceled the application."

["checker.job.cannot_invite.publish"]
hash = "sha1-d257d0e7083d22d8329808f3a214b948f7e9db8b"
other = "This job is not published."

["checker.job.cannot_invite.publish_time"]
hash = "sha1-946a1a23b04f4c1217127f5ba664570e960326a6"
other = "The job has not been published yet."

["checker.job.cannot_invite.will_start"]
hash = "sha1-5d639331025e19ddafe5e5fb9901cf34b1081ad8"
other = "This job will start soon."

["checker.job.cannot_invite.withdraw"]
hash = "sha1-c53eacb2c3ef2ff91e8ca6117dea67c120573b1e"
other = "The professional has already withdrawn the invitation."

["checker.job.cannot_revoke_invite.accept"]
hash = "sha1-fea9543badcd40afa19e8d077fed93ff4f793f5e"
other = "The professional has already accepted the invitation."

["checker.job.cannot_revoke_invite.decline"]
hash = "sha1-674926b4de5ef6929e9f130553e9bcab663109d1"
other = "The professional has already declined the invitation."

["checker.job.cannot_revoke_invite.other"]
hash = "sha1-f08ab391c809640c1fdb494220cf0812ac41249a"
other = "The invitation cannot be revoked."

["checker.job.cannot_withdraw.status"]
hash = "sha1-f25d8efbc6d541a3946fe1702fd92b08fbfe8e9a"
other = "This job application is not in an invitation status."

["checker.job.grand_total_mismatch"]
hash = "sha1-50a82e6361674343b1fd547f1ca86abe7a0ef4c7"
other = "The calculated grand total does not match the provided amount."

["checker.job.hiring_ended"]
hash = "sha1-15f42e9c46915eac4481837f2f1f7bfca8a9a696"
other = "The job hiring has ended."

["checker.job.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.job.invite_max"]
hash = "sha1-213696e7a0091195e17e8ebca27f9c3f9539d9e1"
other = "The job has reached the maximum number of people."

["checker.job.no_available"]
hash = "sha1-76279605ec0330dd73aaf868f10f27a3b7525ab2"
other = "The job is not available."

["checker.job.not_published"]
hash = "sha1-ab078c8c4cc35ab7003c9f6975cc826f797fafcc"
other = "The job is not published."

["checker.job.required_max"]
hash = "sha1-213696e7a0091195e17e8ebca27f9c3f9539d9e1"
other = "The job has reached the maximum number of people."

["checker.job.shift_allocation.cannot_edit"]
hash = "sha1-84faef1237418e80a25f3f7d85d71e514e71b24d"
other = "The job shift allocation cannot be edited."

["checker.job.shift_time.duration_too_long"]
hash = "sha1-57b6d62a7467f08ddc1daaeaf29803f06b8752a2"
other = "Shift duration cannot exceed 24 hours."

["checker.job.shift_time.duration_too_short"]
hash = "sha1-a087a5f35887a0f54db68cf16cc11680cec09043"
other = "Shift duration must be at least 2 hours."

["checker.job.status.cannot_edit"]
hash = "sha1-8351e2651e5f8e735d8fd3cf4bc3d2a9df46decc"
other = "The job status is not editable."

["checker.job.status.cannot_publish"]
hash = "sha1-8d2645ec417df812f7e2eb716fd21e02dae824dc"
other = "The job status is not publishable."

["checker.job.status.cannot_publish.begin_time.greater_than_now"]
hash = "sha1-fb0b3104afc1aef9d72d017733ca7d7a802251eb"
other = "The job begin time must be greater than the current time by 1 hour."

["checker.job.status.cannot_publish.begin_time.required"]
hash = "sha1-1251b1d9e5e4eade7d801b997ea40a3a77b5def4"
other = "The job begin time is required."

["checker.job.status.cannot_publish.shift_time_range"]
hash = "sha1-74708521ce1bd956c16f6e83612685d7eb488cb9"
other = "The job shift time range is not valid."

["checker.job.status.cannot_publish.timezone"]
hash = "sha1-a204d1781527ffbb9cf6d95a628ce4cd3a713cd5"
other = "Please select the service address information."

["checker.job.status.cannot_update"]
hash = "sha1-9cec0891f59ac0d767d25d870603151affdfc503"
other = "The job status is not updatable."

["checker.job.status.cannot_update.begin_time"]
hash = "sha1-2bd199c60aef3b8354d97fafdbde4a3c5c58d8f1"
other = "The job is about to start, cannot be updated."

["checker.job.status.cannot_update.complete"]
hash = "sha1-86dadb5ead2821c3cb349a16ce9ec89e12cc91b9"
other = "The job has completed, cannot be updated."

["checker.job.will_start"]
hash = "sha1-e25d1bd71affa58b35b2c75c50c1130cac0eb61d"
other = "The job is about to start"

["checker.job_application.cannot_cancel"]
hash = "sha1-df95730655b92635eda9a40053b9c6c5a78fc611"
other = "The job application cannot be cancelled."

["checker.job_application.cannot_withdraw.invite"]
hash = "sha1-ad59f26e05a13c6d650ba0a8d5499515c503deae"
other = "You have been invited to join a job by the facility. Please respond to the invitation first."

["checker.job_application.cannot_withdraw.status"]
hash = "sha1-4e805ce943cc5aeeee07364517006fd028c1a6bd"
other = "The job application cannot be withdrawn."

["checker.job_application.id.does_not_exist"]
hash = "sha1-c781e417e82ab4ad2f80cf0d9d298e19dd0e4904"
other = "No such application record, please try after reloading."

["checker.job_file.facility_files.invalid_for_profession"]
hash = "sha1-cfe2ce888d73d42648210dce25ebabe99a2d9adb"
other = "Some files do not exist or are not valid for the specified profession."

["checker.job_file.profession.not_supported"]
hash = "sha1-5473ac20db3149b6f8e0e981ea052b09cce764de"
other = "The specified profession is not supported."

["checker.job_schedule.allowance.not_exist"]
hash = "sha1-5d416d174eb3ee462f9798fb330a312e755d3c17"
other = "Some allowances do not exist."

["checker.job_schedule.cannot_delete.published"]
hash = "sha1-0b715742b07851f44c5b007abe67997b12ded591"
other = "The schedule has published jobs and cannot be deleted."

["checker.job_schedule.id.delete.does_not_exist"]
hash = "sha1-6e5ff113669cd654a111b28ee09de59c5b5d4671"
other = "No such schedule record, please try after reloading."

["checker.job_schedule.id.does_not_exist"]
hash = "sha1-bb961e39fbb9014f16b433d5688bcbc27654caee"
other = "The schedule does not exist."

["checker.job_schedule.shift_time.duration_too_long"]
hash = "sha1-57b6d62a7467f08ddc1daaeaf29803f06b8752a2"
other = "Shift duration cannot exceed 24 hours."

["checker.job_schedule.shift_time.duration_too_short"]
hash = "sha1-a087a5f35887a0f54db68cf16cc11680cec09043"
other = "Shift duration must be at least 2 hours."

["checker.job_schedule.shift_time.empty"]
hash = "sha1-38193127c75078612d0fb2b32df7bea3fd623ada"
other = "Job shift items cannot be empty."

["checker.job_schedule.shift_time.invalid_begin_time"]
hash = "sha1-bc9db6f332334a7f53ba12d88d736ea1e7e87bb9"
other = "Invalid begin time format."

["checker.job_schedule.shift_time.invalid_end_time"]
hash = "sha1-60900ab29b332eb3d2aa2687e4494f1cc50037b1"
other = "Invalid end time format."

["checker.job_schedule.shift_time.invalid_range"]
hash = "sha1-757c49377889d3080e50233f04e0b65ee4bd0e3f"
other = "End time must be after begin time."

["checker.job_schedule.status.has_published_job"]
hash = "sha1-211024a58a79cbff15b2552fd985ae560661b47d"
other = "The completed publication schedule cannot be stopped."

["checker.job_schedule.status.same"]
hash = "sha1-50f3c7b3e95321fbf0daeb77b8fd6e6fbfadd7b0"
other = "The schedule status is already set to the target status."

["checker.job_shift_time.id.can_not_select"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.location.id.cannot_be_parent"]
hash = "sha1-05b4bb191e90298cbcf69f97914b8704dec918d8"
other = "The parent location is incorrect."

["checker.location.id.cannot_delete"]
hash = "sha1-95a5bda68f6d0f1a636a72e2220a67570844cff8"
other = "The location cannot be deleted."

["checker.location.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.menu.code.already_exists"]
hash = "sha1-ab70c226ffd7949daa18886012362396a781aef7"
other = "The menu code is already exists. Please try another code."

["checker.menu.id.cannot_be_deleted"]
hash = "sha1-af5fb8be3b25bdd22943b5784d64a61c0b0742b0"
other = "This menu cannot be deleted."

["checker.menu.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.professional.abn_cancelled"]
hash = "sha1-b7066a23d25ad6dc10ca6252d5255ed027bc99c5"
other = "The ABN is cancelled."

["checker.professional.abn_invalid"]
hash = "sha1-abbaeff49cdd71eb6761cebed96a060aa0e699cf"
other = "The ABN is invalid."

["checker.professional.already_init"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.professional.already_submitted"]
hash = "sha1-c9858e14db9faf7aa735ad76996ba55c504a46da"
other = "The profile has already been submitted."

["checker.professional.can_edit"]
hash = "sha1-c4c70a032c6da4c363d4c1c2679ed698385a05c2"
other = "The current status cannot be edited."

["checker.professional.can_not_approve"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.professional.can_submit"]
hash = "sha1-bf94ccf2e4e1c5c988377c313aae812bdd3a7947"
other = "The profile is not complete, please fill in completely."

["checker.professional.can_withdraw"]
hash = "sha1-1ee85357f0956569104b19b5b2d85f3340fafb30"
other = "The profile cannot be withdrawn."

["checker.professional.experience_level_invalid"]
hash = "sha1-e830b2e815e9e557c596d207f27c33c8aa7bcd9b"
other = "Experience level does not match qualification end date."

["checker.professional.file_expiration_invalid"]
hash = "sha1-939bdba108701b092cb1db2c44cb7791b125f0db"
other = "{{.Name}} expiration date is invalid."

["checker.professional.file_expiration_invalid.criminal"]
hash = "sha1-f18d7e7e12111e5aa998d67ad3a2940e3c8eb2d8"
other = "The National Criminal Check Expiry Date must be at least 3 years ago."

["checker.professional.file_expiration_invalid.passport"]
hash = "sha1-d1302a7330568054b0927676e557032171b8c674"
other = "The passport must be valid or expired within 2 years."

["checker.professional.file_expiration_invalid.today"]
hash = "sha1-5dd887e7bef6f039db4406f5b0840583f764dc3a"
other = "{{.Name}} has expired"

["checker.professional.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.professional.id_check_file_types_invalid"]
hash = "sha1-dcb39b0461aaae79d7c9b89e252b17a8d4cf567c"
other = "ID Check file types do not match uploaded files."

["checker.professional.missing_update_prompt"]
hash = "sha1-6a986bf3af5381d9be1c5a708beba6bba78c381f"
other = "Please fill in the update prompt."

["checker.professional.preferred_grade_invalid"]
hash = "sha1-9e477a84e5acc2483cb4eec03df1757a3f11dcb6"
other = "Preferred Grade does not match Experience Level."

["checker.professional.preferred_specialities"]
hash = "sha1-d4fd50be61d3c7de8962e98004825fe88c279cfd"
other = "The preferred specialities is not valid."

["checker.professional.references_pending_approval"]
hash = "sha1-157cbd7f4c4a04b0dbd0a7698c2e2bd751271706"
other = "Referee information pending approval. Please review and approve the referee details first."

["checker.professional.working_with_children_states_mismatch"]
hash = "sha1-8261e2f7fdc77c6941a85742cdd1d1de7d45ad9b"
other = "Working with children/vulnerable people states do not match file descriptions."

["checker.professional_file.edit.missing_parameter"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.professional_file.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.professional_file.id.facility.does_not_exist"]
hash = "sha1-85019294151b823ceb74f3448711d983f5ad4bda"
other = "No such file, please try again later."

["checker.professional_superannuation.declaration.not_confirmed"]
hash = "sha1-ed4b1836e65dcaeeb06c99b83bcba83e20080ae9"
other = "Declaration must be confirmed when submitting superannuation information."

["checker.professional_superannuation.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.recaptcha.check_failed"]
hash = "sha1-d7ed05a7802a24ac9dd9153471690a73afe2752a"
other = "reCAPTCHA verification failed"

["checker.register.code.expired"]
hash = "sha1-f54f17749cbaa22a4fcb5c190deb5858af1d8c39"
other = "The verification code has expired. Please resend the verification code."

["checker.register.code.send.count.limit"]
hash = "sha1-b13fad5f87e0da92fe156b1566378fcbc525cc86"
other = "The registration request has timed out. Please register again."

["checker.register.info.not_exists"]
hash = "sha1-b13fad5f87e0da92fe156b1566378fcbc525cc86"
other = "The registration request has timed out. Please register again."

["checker.register.user.email.already_exists"]
hash = "sha1-88ed2493d5b96963350f6be6832dd670f08296ec"
other = "Email already registered. Please try another email."

["checker.register.verification.code.invalid"]
hash = "sha1-850dfb86eed97c4ec827e87762dd100709968f59"
other = "Invalid verification code."

["checker.role.action.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.role.id.cannot_delete"]
hash = "sha1-5b155e8befb3c025f8d5ddb38a76fc6ef9ac4649"
other = "This role cannot be deleted."

["checker.role.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.role.name.already_exists"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.selection.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.service_location.id.does_not_exist"]
hash = "sha1-995c3c193b6e89805f0e317968ba3c9abe0e13fe"
other = "No such record, please try after reloading."

["checker.training.module.access_denied"]
hash = "sha1-0a63af714c1143aa48e9fa7eb068dece00e68df8"
other = "Access denied. Please complete the previous module first."

["checker.training.module.already_completed"]
hash = "sha1-77c7faae7e9b1f5b6c873a1cad8e9062fabc9050"
other = "All questions in this module have been completed correctly."

["checker.training.module.incomplete_answers"]
hash = "sha1-029c173be8d5b3f521ff42029479f5e0026c6374"
other = "Please answer all questions in this module before submitting."

["checker.training.module.not_found"]
hash = "sha1-91b476988811df862f546bd58de57e683928340a"
other = "Training module not found."

["checker.training.module.prev_not_found"]
hash = "sha1-b26de0b53b113fc2e3890c840e320379c3aeacf2"
other = "Previous training module not found."

["checker.training.module.professional.all.not_completed"]
hash = "sha1-8ffe148fa52900a09a0255d48c80988be67d05f1"
other = "Please complete all training first."

["checker.training.video.not_watched"]
hash = "sha1-02a19b5c20661bc18085e291435c15f082252360"
other = "Please watch the training video first."

["checker.user.email.already_exists"]
hash = "sha1-16b3ebb1d055464a8f986fe1082221fcfd97ee2a"
other = "Email already exists."

["checker.user.email.does_not_exist"]
hash = "sha1-ef3a9883c6510a0880d8867ed8e31b5a0e82ea33"
other = "This email address is not registered."

["checker.user.id.cannot_delete"]
hash = "sha1-ce4d79efd20da8dc049645bcda32b259cd02d0fd"
other = "This user cannot be deleted."

["checker.user.id.does_not_exist"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.ws_message.id.check_message_failed"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.ws_message.id.facility_cannot_send_to_professional"]
hash = "sha1-68123a35f3eee89054e456c045a807d6b9798f05"
other = "cannot send messages to professional."

["checker.ws_message.id.handle_message_failed"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["checker.ws_message.id.message_already_processed"]
hash = "sha1-9d42da3a25092855ab9fa3521430673ddffa589d"
other = "Message already processed."

["checker.ws_message.id.message_not_found"]
hash = "sha1-fc0b1093ef3629d1ba60378e1e5f9a499b00472d"
other = "Message not found."

["checker.ws_message.id.message_type_not_allowed"]
hash = "sha1-37506b032392d7c31cc2fb1a357c4e12b2988c67"
other = "Message type not allowed."

["checker.ws_message.id.professional_cannot_send_to_facility"]
hash = "sha1-41a28a17a9f4884e079d9bdae3f9e05ba80e00ce"
other = "cannot send messages to facility."

["checker.ws_message.id.required_param_missing"]
hash = "sha1-b63961c9d38546b574f3f06e2d958e1eb5551d10"
other = "Required parameter missing."

["checker.ws_message.id.session_not_found"]
hash = "sha1-9d9a0b7ada9d81eeef3a2ca244208ccbe940be7b"
other = "Session not found."

["checker.ws_message.id.unmarshal_message_failed"]
hash = "sha1-8e6e998ca90552f944652fdd989d295ce547ce31"
other = "Some thing went wrong, please try again later."

["facility_agreement.email.button.text"]
hash = "sha1-af550498ca7cba730962ce183d29543a8f288720"
other = "Review Agreement"

["facility_agreement.email.greeting"]
hash = "sha1-f7ff9e8b7bb2e09b70935a5d785e0cc5d9d0abf0"
other = "Hello"

["facility_agreement.email.instructions"]
hash = "sha1-7f1e5260855ff54c1e9c3564c3615094aee951a9"
other = "To proceed, please log into our system to complete your facility's details and sign the agreement. You may use the same login credentials (email and password) that were used during your initial registration."

["facility_agreement.email.intros"]
hash = "sha1-5b98659d4a13a28adbcb32fa5669870b6820b418"
other = "We have reviewed and verified the information provided by your facility, and a draft agreement has now been prepared accordingly."

["facility_agreement.email.outros"]
hash = "sha1-d204b5e01e60386a2858e752979b3e45b38d8ae3"
other = "Should you have any questions or require assistance, please do not hesitate to contact us."

["facility_agreement.email.signature"]
hash = "sha1-94c4021ba7d2aafde3c6dc383b6913dda61d22ea"
other = "Kind regards,"

["facility_agreement.email.subject"]
hash = "sha1-68b0df59c21baaa58bed8ecdd4253b44440abd81"
other = "Agreement Ready for Review and Completion on Medic Crew"

["forget.password.email.button.text"]
hash = "sha1-30853959c97631fe507e669c1fbfccfdb409025f"
other = "RESET "

["forget.password.email.greeting"]
hash = "sha1-94dd9e08c129c785f7f256e82fbe0a30e6d1ae40"
other = "Hi"

["forget.password.email.instructions"]
hash = "sha1-261f6071eeed3acd2fe703e1b54e77982677c0b7"
other = "Please click the button below to reset your password."

["forget.password.email.intros"]
hash = "sha1-4689e3828bfb24ff339557b8507c967a68840d69"
other = "We Receive your forgot your password message."

["forget.password.email.outros"]
hash = "sha1-8407583d884c43c9141fa906ee93508e1856c68c"
other = "Please reset your password within 30 minutes, re-apply is needed after expiration."

["forget.password.email.signature"]
hash = "sha1-1daec180566cb1c61d951fe6cf12eae00b04172d"
other = "Thank you very much!"

["forget.password.email.subject"]
hash = "sha1-5c4bc97ee5d0ac344829dbcef02d7302feb098a8"
other = "Reset password"

["forget.password.email.trouble.text"]
hash = "sha1-7b03794237ed953d8d9b0c02d5474ec61fb75725"
other = "If you have any problem to open the button '{ACTION}', please copy and paste the link below to your web browser."

["get.user.login.forget.password.fail"]
hash = "sha1-fd18a7b2ab5bfa851597ccf735b8a4fbbe08bf88"
other = "The reset password link you opened is invalid. Please re-enter your email address below and send it."

["job_application.session_not_found"]
hash = "sha1-4c2e72c72aa0085f23786899283451fdbf918d90"
other = "Session not found"

["job_schedule.begin_date.invalid"]
hash = "sha1-a1341c397cbb97e904a74b65938d2fffd0a8a2d0"
other = "Invalid start date format."

["job_schedule.daily_interval.required"]
hash = "sha1-9a5d5153f26bb8ce63969d40d2c02607a5706a30"
other = "Daily repeat interval must be greater than 0."

["job_schedule.date_range.invalid"]
hash = "sha1-5283d80149108f9b5a9e6fa52e0b40bfe26765f8"
other = "End date cannot be earlier than start date."

["job_schedule.end_date.invalid"]
hash = "sha1-0eac0d7dbfdc05fdd27e80c608ecf8f29073928e"
other = "Invalid end date format."

["job_schedule.monthly_day_of_month.invalid"]
hash = "sha1-b29d06182f3877550165084d833027f6fe21eeb8"
other = "Day of month must be between 1 and 31."

["job_schedule.monthly_interval.required"]
hash = "sha1-a38d77d43d093442d0f5c8c2479cf2dfad452bbc"
other = "Monthly repeat interval must be greater than 0."

["job_schedule.monthly_type.invalid"]
hash = "sha1-723e005dcf5ad74d84ab95f3270cf7681ff138e3"
other = "Invalid monthly repeat type."

["job_schedule.monthly_week_index.invalid"]
hash = "sha1-e61148bdc3d5d83252df3f8c59e56a12d866f423"
other = "Week index must be between 1 and 5."

["job_schedule.no_valid_dates"]
hash = "sha1-51d7780567d2a646dd7d93604778ef55829d4f4e"
other = "No valid job dates will be generated with the current settings."

["job_schedule.repeat_type.invalid"]
hash = "sha1-7a79f7c8f52d08b7700331c462f7b1ca0f9acc19"
other = "Invalid repeat type."

["job_schedule.shift_time.begin_time.invalid"]
hash = "sha1-bc9db6f332334a7f53ba12d88d736ea1e7e87bb9"
other = "Invalid begin time format."

["job_schedule.shift_time.end_time.invalid"]
hash = "sha1-60900ab29b332eb3d2aa2687e4494f1cc50037b1"
other = "Invalid end time format."

["job_schedule.shift_time.overlap"]
hash = "sha1-addec1535d0b61b95250263ea3ba2d284449d402"
other = "Shift time overlaps."

["job_schedule.shift_time.required"]
hash = "sha1-e1d8ff7a85803a400294f56d69bceded5cb6617c"
other = "Shift time is required."

["job_schedule.timezone.invalid"]
hash = "sha1-82698d39fdb1ea0523945746b037c1ffaa0f0736"
other = "Invalid timezone."

["job_schedule.week_days.required"]
hash = "sha1-9b16312bf8516afa081c27fe851f7f7bcb10e0f1"
other = "At least one weekday must be specified for weekly repeats."

["job_schedule.weekly_interval.required"]
hash = "sha1-fdad58a56b7cee20ed4a084d93ede7ebfd1f19cd"
other = "Weekly repeat interval must be greater than 0."

["jwt.authentication.expired"]
hash = "sha1-60d8f3ed48ed37fc139c4f23ae66d1cd242a2bb7"
other = "登入信息已過期，請重新登入。"

["jwt.authentication.failed"]
hash = "sha1-f942dffe5deeaf9e1b627b62dc72d55ee2c4806f"
other = "登入信息有誤，請重新登入。"

["login.failed"]
hash = "sha1-1791ecc7d2df48cdc3180cdad12386908d1e3355"
other = "Incorrect account or password."

["oauth_login.failed"]
hash = "sha1-a35cb1b709b2dbb7494d4a4d12214f207ae40898"
other = "Unable to sign in this account."

["professional.profile.field.abn"]
hash = "sha1-b270984141db41f48ca4bd1b8d8f8d5ddec880af"
other = "ABN"

["professional.profile.field.additional_certification"]
hash = "sha1-e42e964a556f43d4586423bf4d800dd7596a661a"
other = "Additional Certification"

["professional.profile.field.address"]
hash = "sha1-d70f93df5e8f9b55be44fbeee9d203972e3383d4"
other = "Address"

["professional.profile.field.address_extra"]
hash = "sha1-2b32a58cbb1288dd67a59ee2831d1d8b643d66c8"
other = "Address Extra"

["professional.profile.field.ahpra_certificate"]
hash = "sha1-557c11e7b9be1c35970bbb4418861578705c7106"
other = "AHPRA Certificate"

["professional.profile.field.australian_birth_certificate"]
hash = "sha1-21406fcd1ee757a5dd36da1a2603fa4496580d6c"
other = "Australian Birth Certificate"

["professional.profile.field.australian_citizenship_certificate"]
hash = "sha1-7dd7512162b4ed8c85244f8b78ac41240bb4f538"
other = "Australian Citizenship Certificate"

["professional.profile.field.australian_passport"]
hash = "sha1-729c309e815f0e308c1194586caa9c2cdcade5c7"
other = "Australian Passport"

["professional.profile.field.australian_public_service_employee_id_card"]
hash = "sha1-aa66b532ade908fcf2c90bed1f245ec8328cb0b3"
other = "Australian Public Service Employee ID Card"

["professional.profile.field.centrelink_or_pension_card"]
hash = "sha1-0f7faf2a6f866f3a7c7c4e1b24b775cd29085499"
other = "Centrelink Or Pension Card"

["professional.profile.field.commonwealth_statutory_declaration"]
hash = "sha1-0917b87917b5a5d056ca5db8808b4b7852602473"
other = "Commonwealth Statutory Declaration"

["professional.profile.field.completed_studies_in_last_three_years"]
hash = "sha1-1bef799d3bb16d400dbb0a17b832989c1cfc6d44"
other = "Completed Studies In Last Three Years"

["professional.profile.field.credit_debit_atm_card"]
hash = "sha1-04be8d01d1cf63f44801040426faaf76ece02401"
other = "Credit Debit ATM Card"

["professional.profile.field.current_australia_driver_licence"]
hash = "sha1-bd797ae1933583db64a5081ea30ca798e55d08c6"
other = "Current Australia Driver Licence"

["professional.profile.field.current_immunisation_records"]
hash = "sha1-1af734c6f98b8c7a4183ef706e1019903999af45"
other = "Current Immunisation Records"

["professional.profile.field.date_of_birth"]
hash = "sha1-f25210a5310fd3f221d3343bedff76ac67c2a269"
other = "Date Of Birth"

["professional.profile.field.disclosure"]
hash = "sha1-4689ae6759b459e08a0427a25cfd90802dd164b9"
other = "Disclosure"

["professional.profile.field.disclosure_questions"]
hash = "sha1-7e64982d720709683340e9f198c304d9f27ba6de"
other = "Disclosure Questions"

["professional.profile.field.distance_within"]
hash = "sha1-621144862fb741090b6065a42972fa3cd4e18338"
other = "Distance Within"

["professional.profile.field.emergency_contact_first_name"]
hash = "sha1-9bba9be022c7d6db9fed54eae034e6d8305ef384"
other = "Emergency Contact First Name"

["professional.profile.field.emergency_contact_last_name"]
hash = "sha1-a88d26a3280eff5df7bf08a8d509b02ec985859d"
other = "Emergency Contact Last Name"

["professional.profile.field.emergency_contact_phone"]
hash = "sha1-5aee4e58938a7be05affa1413d1ca21e4abdae49"
other = "Emergency Contact Phone"

["professional.profile.field.emergency_contact_relationship"]
hash = "sha1-490df9c20f38a221b91335a3aa847066bdf538f4"
other = "Emergency Contact Relationship"

["professional.profile.field.experience_level"]
hash = "sha1-c4c6c46f6fa0802e7f6e2280475e0b970c402a0f"
other = "Experience Level"

["professional.profile.field.experiences"]
hash = "sha1-522712582116fcc6e5a881399c84f72c4d5508a6"
other = "Experiences"

["professional.profile.field.first_name"]
hash = "sha1-b6ea992aab4668311bb94778e056dd0285f27621"
other = "First Name"

["professional.profile.field.foreign_passport"]
hash = "sha1-549406ef4197911c9185b41e286e70f230ec8260"
other = "Foreign Passport"

["professional.profile.field.gender"]
hash = "sha1-8a754c61c2ced0c5ff79a1827e02c9d643d6d926"
other = "Gender"

["professional.profile.field.has_completed_infection_control_training"]
hash = "sha1-8053e771a61c0b99e55ddec46292b7522503082c"
other = "CPR/BLS Certification"

["professional.profile.field.has_overseas_citizenship_or_pr"]
hash = "sha1-e51dfe385724e73d132bb81f045d5eecd55996e5"
other = "Citizenship Since Age 16"

["professional.profile.field.indemnity_insurance_certificate"]
hash = "sha1-8d0579a4ab2704eef9c676d7ba465fc322a6d7a0"
other = "Indemnity Insurance Certificate"

["professional.profile.field.last_name"]
hash = "sha1-863cb39fbe7d70597076af1960b7ae4618d9e1bc"
other = "Last Name"

["professional.profile.field.medicare_card"]
hash = "sha1-1665edf1e210a90795825ff37e06f535f97db132"
other = "Medicare Card"

["professional.profile.field.minimum_hourly_rate"]
hash = "sha1-7900c207b077de19176a9ec74b7d106619dfb628"
other = "Minimum Hourly Rate"

["professional.profile.field.national_criminal_check"]
hash = "sha1-996cfe1cce7ca88cbd8122c75536e10e789ba9ab"
other = "National Criminal Check"

["professional.profile.field.other_australian_government_issue_id_card"]
hash = "sha1-c600ffa20a6f193e9de40671772031b912f25d77"
other = "Other Australian Government Issue ID Card"

["professional.profile.field.permission_to_work"]
hash = "sha1-2d4ee5bd930a31ca31cda45ed26efa43cd935bc2"
other = "Permission To Work"

["professional.profile.field.personal_care_worker_qualification_bachelor_nursing"]
hash = "sha1-6db07c8719b541cbde3ddc32b5a4afe3dc71c114"
other = "Bachelor of Nursing"

["professional.profile.field.personal_care_worker_qualification_certificate_iii_aged_care"]
hash = "sha1-9380758157df3aea10794a13695ade0ec62845d0"
other = "Certificate III Aged Care"

["professional.profile.field.personal_care_worker_qualification_certificate_iii_disabilities"]
hash = "sha1-558ecac494cf38320407300e51edec3970d34348"
other = "Certificate III in Disabilities"

["professional.profile.field.personal_care_worker_qualification_certificate_iii_home_community_care"]
hash = "sha1-b80c581e6215cc222cbbbd455712eacc60817bce"
other = "Certificate III in Home and Community Care"

["professional.profile.field.personal_care_worker_qualification_certificate_iii_individual_support"]
hash = "sha1-07a9340c641000715836ac9a8eeefad9548802a8"
other = "Certificate III Individual Support"

["professional.profile.field.personal_care_worker_qualification_certificate_iv_ageing_support"]
hash = "sha1-fd4e3589c5e0e1033c8be5146682a115489a30ea"
other = "Certificate IV in Ageing Support"

["professional.profile.field.personal_care_worker_qualification_certificate_iv_disability"]
hash = "sha1-3e4e9dbd62a6dc7e9d3fb7af6ce172e7b9563a55"
other = "Certificate IV in Disability"

["professional.profile.field.personal_care_worker_qualification_certificate_iv_home_community_care"]
hash = "sha1-4149e453f46b0d81461404117523a85f6aa82aee"
other = "Certificate IV in Home and Community Care"

["professional.profile.field.personal_care_worker_qualification_diploma_nursing"]
hash = "sha1-c21db992aeceaab3b804fb690b401e1d6ed3ead3"
other = "Diploma of Nursing"

["professional.profile.field.photo"]
hash = "sha1-d01d900383e44b2374d80860d4d86b317de21077"
other = "Photo"

["professional.profile.field.preferred_grade"]
hash = "sha1-d34d89d497dd8f7c7ee9a65bddb43bbb19272acf"
other = "Preferred Grade"

["professional.profile.field.preferred_locality"]
hash = "sha1-b9aee26ea1e7dee7eed71e17579c80f014ce0261"
other = "Preferred Location"

["professional.profile.field.preferred_specialities"]
hash = "sha1-40fb7d41c4a77f569cd1c143555b115fb10f21d2"
other = "Preferred Specialities"

["professional.profile.field.preferred_speciality_other_name"]
hash = "sha1-23e4d3418a80c0d5de3e797024703e71bd0f676f"
other = "Preferred Speciality Other Name"

["professional.profile.field.preferred_state"]
hash = "sha1-b9aee26ea1e7dee7eed71e17579c80f014ce0261"
other = "Preferred Location"

["professional.profile.field.profession"]
hash = "sha1-6f528041b7e1477e50097fc53d515f6ffe1b285f"
other = "Profession"

["professional.profile.field.qualification"]
hash = "sha1-2ffe44e547583bf61e0c2b02d0288df152e1e76d"
other = "Qualification"

["professional.profile.field.qualification_end_date"]
hash = "sha1-05853be2b4c3283aa0f98d275271addcd54a1647"
other = "Qualification End Date"

["professional.profile.field.references"]
hash = "sha1-5d20d0fee3b91643dd8d272ac33d01ca95179d82"
other = "References"

["professional.profile.field.statement_from_financial_institution"]
hash = "sha1-92bf2f137e63b9c5ac321a0fe1a7db7b4660a878"
other = "Statement From Financial Institution"

["professional.profile.field.tertiary_student_id_card"]
hash = "sha1-dc3b863edd251ff0db48f4d45e5a2cdfdd24cdfd"
other = "Tertiary Student ID Card"

["professional.profile.field.utility_bill_or_rate_notice"]
hash = "sha1-6b307852bbd267cececdef64c055d6378562cd5f"
other = "Utility Bill Or Rate Notice"

["professional.profile.field.visa"]
hash = "sha1-8201464a0e80138777b8c32237634ea4180dafcb"
other = "Visa"

["professional.profile.field.working_with_children_or_vulnerable_people"]
hash = "sha1-7ed4525b02bcc595f8d6015ac623228c4ef01b77"
other = "Working With Children / Vulnerable People Check"

["professional_file.name.abn"]
hash = "sha1-b270984141db41f48ca4bd1b8d8f8d5ddec880af"
other = "ABN"

["professional_file.name.additional_certification"]
hash = "sha1-e42e964a556f43d4586423bf4d800dd7596a661a"
other = "Additional Certification"

["professional_file.name.ahpra_certificate"]
hash = "sha1-8861fec94a06617f367d025c1bfad7cf4cd45492"
other = "AHPRA Registration"

["professional_file.name.australian_birth_certificate"]
hash = "sha1-d66b31520b32bd16619774b2ec94578c177cea09"
other = "Australian birth certificate"

["professional_file.name.australian_citizenship_certificate"]
hash = "sha1-f2e47b8d6bde6953b4a8c1bb96f350880cc212f9"
other = "Australian citizenship certificate"

["professional_file.name.australian_passport"]
hash = "sha1-7357c065d246111562371d0fce99061a2ff9356e"
other = "Australian passport (current or expired less than 2 years ago)"

["professional_file.name.australian_public_service_employee_id_card"]
hash = "sha1-94f5f177508c03e7dc3161f029b5f66552076ef7"
other = "Australian Public Service employee ID card with photo"

["professional_file.name.centrelink_or_pension_card"]
hash = "sha1-170c75182c2829a84e356d43cb28f62764eb73a7"
other = "Centrelink or pension card"

["professional_file.name.commonwealth_statutory_declaration"]
hash = "sha1-4aa0c4ef598d06b00642fe392bb1298f307b1436"
other = "Overseas Residency / Citizenship"

["professional_file.name.credit_debit_atm_card"]
hash = "sha1-ba8d2d6ee636274891bb56543e084942f26e4fb1"
other = "Credit/Debit/ATM Card (maximum of one card from any one financial institution)"

["professional_file.name.current_australia_driver_licence"]
hash = "sha1-a2ebc347f0fc7a8402abd0bbe45f505de3955cb4"
other = "Current Australia driver licence"

["professional_file.name.current_immunisation_records"]
hash = "sha1-1af734c6f98b8c7a4183ef706e1019903999af45"
other = "Current Immunisation Records"

["professional_file.name.curriculum_vitae"]
hash = "sha1-777615c52dbd9acd321da99d86b1972d4adab4ec"
other = "Experience CV"

["professional_file.name.disclosure"]
hash = "sha1-26dfc22b1abe6d5c3385c4db50edabde0457e720"
other = "Disclosures"

["professional_file.name.fellowship_certificate"]
hash = "sha1-cf76b0dd1f1103af56f86a7964f7a12ce1b70e66"
other = "Areas of Experience Fellowship Certificate"

["professional_file.name.foreign_passport"]
hash = "sha1-896f4e7786dbbd7300392aff822fa0a994bfe5f8"
other = "Foreign passport"

["professional_file.name.indemnity_insurance_certificate"]
hash = "sha1-8d0579a4ab2704eef9c676d7ba465fc322a6d7a0"
other = "Indemnity Insurance Certificate"

["professional_file.name.medicare_card"]
hash = "sha1-d691b434661e25ba90a0a114a0d410c32f5b34c0"
other = "Medicare card"

["professional_file.name.national_criminal_check"]
hash = "sha1-996cfe1cce7ca88cbd8122c75536e10e789ba9ab"
other = "National Criminal Check"

["professional_file.name.other_australian_government_issue_id_card"]
hash = "sha1-53a1cb75c0a75f74e65d7fd0f580cf244a3d5778"
other = "Other Australian government issue ID card with photo"

["professional_file.name.personal_care_worker_qualification_bachelor_nursing"]
hash = "sha1-6db07c8719b541cbde3ddc32b5a4afe3dc71c114"
other = "Bachelor of Nursing"

["professional_file.name.personal_care_worker_qualification_cert_iii_disabilities"]
hash = "sha1-558ecac494cf38320407300e51edec3970d34348"
other = "Certificate III in Disabilities"

["professional_file.name.personal_care_worker_qualification_cert_iii_individual_support"]
hash = "sha1-07a9340c641000715836ac9a8eeefad9548802a8"
other = "Certificate III Individual Support"

["professional_file.name.personal_care_worker_qualification_cert_iv_ageing_support"]
hash = "sha1-fd4e3589c5e0e1033c8be5146682a115489a30ea"
other = "Certificate IV in Ageing Support"

["professional_file.name.personal_care_worker_qualification_cert_iv_disability"]
hash = "sha1-3e4e9dbd62a6dc7e9d3fb7af6ce172e7b9563a55"
other = "Certificate IV in Disability"

["professional_file.name.personal_care_worker_qualification_certificate_iii_aged_care"]
hash = "sha1-9380758157df3aea10794a13695ade0ec62845d0"
other = "Certificate III Aged Care"

["professional_file.name.personal_care_worker_qualification_certificate_iii_home_community_care"]
hash = "sha1-b80c581e6215cc222cbbbd455712eacc60817bce"
other = "Certificate III in Home and Community Care"

["professional_file.name.personal_care_worker_qualification_certificate_iv_home_community_care"]
hash = "sha1-4149e453f46b0d81461404117523a85f6aa82aee"
other = "Certificate IV in Home and Community Care"

["professional_file.name.personal_care_worker_qualification_diploma_nursing"]
hash = "sha1-c21db992aeceaab3b804fb690b401e1d6ed3ead3"
other = "Diploma of Nursing"

["professional_file.name.photo"]
hash = "sha1-4dbfd0986a9e1476ed67d7e93a6ade69154fe25c"
other = "Profile Photo"

["professional_file.name.qualification_certificate"]
hash = "sha1-339861320025da1877665fda46943a16ff25c81d"
other = "Graduating Institution"

["professional_file.name.registrar_accredited_enrolment"]
hash = "sha1-c0561362c89f8e2a97f082c7298d22831fde9744"
other = "Areas of Experience Registrar (Accredited) Enrolment"

["professional_file.name.signed_agreement"]
hash = "sha1-053dcdd5d87e5f26e08aeb3abe7248f1ce045a7b"
other = "Signed Agreement"

["professional_file.name.specialist_qualification"]
hash = "sha1-e4cd42ccba7f19fc989d62315f7300a3a7b9ef21"
other = "Areas of Experience Specialist Qualification"

["professional_file.name.statement_from_financial_institution"]
hash = "sha1-49c7fde819ba8a1cb5dbfe33b9b7c4620301031e"
other = "Statement from a financial institution where you have held the account for at least one year"

["professional_file.name.tertiary_student_id_card"]
hash = "sha1-0ada9e9b6b22a98747294fe183dd6311d8ac1152"
other = "Tertiary student ID card with photo"

["professional_file.name.utility_bill_or_rate_notice"]
hash = "sha1-752601bb69db61e5d80f01f8e165984c2d71bcf5"
other = "Utility bill or rate notice, for example water rates, council rates, electricity or gas; must be less than 12 months old"

["professional_file.name.visa"]
hash = "sha1-8201464a0e80138777b8c32237634ea4180dafcb"
other = "Visa"

["professional_file.name.working_with_children_or_vulnerable_people"]
hash = "sha1-7ed4525b02bcc595f8d6015ac623228c4ef01b77"
other = "Working With Children / Vulnerable People Check"

["reference_form.email.button1.text"]
hash = "sha1-bca6694069f85cf197d7ec0dde695a2222543835"
other = "Complete reference"

["reference_form.email.greeting"]
hash = "sha1-94dd9e08c129c785f7f256e82fbe0a30e6d1ae40"
other = "Hi"

["reference_form.email.instructions"]
hash = "sha1-53b096bdcedf56884dcb395069f403ea40f758e6"
other = "To complete your reference for {{.ProfessionalName}}"

["reference_form.email.intro"]
hash = "sha1-896b43c28b60f55a24b29ca4fe514e07e73325ea"
other = "{{.ProfessionalName}} has registered as {{.AOrAn}} {{.ProfessionName}} on the Medic Crew platform and nominated you as a referee.\n"

["reference_form.email.intro1"]
hash = "sha1-a609dcdf84ef20fbc3aa53749a774803f8c1ac7c"
other = "{{.ProfessionalName}} is seeking to provide the following services:\n"

["reference_form.email.intro2"]
hash = "sha1-419b93903a3f5080b9444a918bb32f2326c21407"
other = "Medic Crew helps connect healthcare providers with facilities seeking medical services, ensuring that practitioners with the right skills and interests are matched with facilities' needs. For More, visit www.mediccrew.com.au\n"

["reference_form.email.intro3"]
hash = "sha1-42b18a4ea0415194e39f3d09f1a610ed27dedcef"
other = "Thank you for taking the time to complete this request. Your timely response is appreciated as it will help us speed up the process for {{.ProfessionalName}}. You can do this using your mobile device and it should take no longer than a few minutes.\n"

["reference_form.email.signature"]
hash = "sha1-6b50e31a7e1337db9ecb0e2fcb6e65ca27f57df5"
other = "Thank you"

["reference_form.email.subject"]
hash = "sha1-43bf4539d3d0ecb7d7905ab6d36deb4cbe162c5c"
other = "Kindly complete a reference for {{.ProfessionalName}}"

["reference_form.email_verify.greeting"]
hash = "sha1-94dd9e08c129c785f7f256e82fbe0a30e6d1ae40"
other = "Hi"

["reference_form.email_verify.intro"]
hash = "sha1-826e15c69cff7ce1151e449b2b9700cb9218b691"
other = "You have been invited to provide a reference for {{.ProfessionalName}} on Medic Crew."

["reference_form.email_verify.intro1"]
hash = "sha1-6b5290fc4148d29886cf24098e1b23805f5eccb9"
other = "To confirm your identity, please use the verification code below:"

["reference_form.email_verify.intro2"]
hash = "sha1-505d1d5a659670f81d8f33b170c830f26c4d9e43"
other = "Please enter this code on the verification page to proceed."

["reference_form.email_verify.signature"]
hash = "sha1-6b50e31a7e1337db9ecb0e2fcb6e65ca27f57df5"
other = "Thank you"

["reference_form.email_verify.subject"]
hash = "sha1-cfe49ba9dd31ef38d9b92e143bd0c5eec212d0fb"
other = "Referee Identity Verification"

["register.email.greeting"]
hash = "sha1-94dd9e08c129c785f7f256e82fbe0a30e6d1ae40"
other = "Hi"

["register.email.instructions"]
hash = "sha1-c95c0df29642c13c554c3ec6e8a6545cd154e820"
other = "Below is your registration activation code. Please enter it within 10 minutes."

["register.email.intros"]
hash = "sha1-a90d3249371829729219340caab740243e2d8857"
other = "Thank you for registering as a Medic Crew user."

["register.email.signature"]
hash = "sha1-6b50e31a7e1337db9ecb0e2fcb6e65ca27f57df5"
other = "Thank you"

["register.email.subject"]
hash = "sha1-dc324a9b2f98d1fcdbfb87fc45c637a07baa326d"
other = "Registration Verification Code"

["user.status.disable"]
hash = "sha1-caadd86a30632cd335222ff44514b7c76dd804d4"
other = "This account is currently unable to sign in."

["user.type.not.please_use_username_and_id"]
hash = "sha1-f6cc2efbe816cbb28f60372a0843fd60edd8d1e8"
other = "Please use username and password to login."

["user.type.not.support_oauth"]
hash = "sha1-b37531937a415c892f81efc2c1d25c39586e5bca"
other = "This account is not supported to sign in with OAuth."

["user_devices.apply.email.greeting"]
hash = "sha1-94dd9e08c129c785f7f256e82fbe0a30e6d1ae40"
other = "Hi"

["user_devices.apply.email.instructions"]
hash = "sha1-6a14a2573f3c55265d2d098ad939b5e29c4f5642"
other = "The following is the verification code, please fill in within 10 minutes."

["user_devices.apply.email.intros"]
hash = "sha1-97a336b10ba40a048177159f8df33f6ea777348d"
other = "A sign in attempt requires further verification because we did not recognize your device. To complete the sign in, enter the verification code on the unrecognized device."

["user_devices.apply.email.outros"]
hash = "sha1-dce059db4f44e2d24a78c3c674b6e75520ce094f"
other = "If you did not attempt to sign in to your account, your password may be compromised. You may need to create a new, strong password for your account."

["user_devices.apply.email.signature"]
hash = "sha1-97cba4858411c435e526dd651157aac20178222c"
other = "Thanks"

["user_devices.apply.email.subject"]
hash = "sha1-d636269fae954c1ed9243fd8d0f00e152668d336"
other = "Please verify your device"

["user_devices.apply.failed"]
hash = "sha1-1791ecc7d2df48cdc3180cdad12386908d1e3355"
other = "Incorrect account or password."

["user_devices.apply.no_email"]
hash = "sha1-be0712f12547072f1ee344acfabab4fc1537b75f"
other = "Please setup your email first."

["user_devices.not_found"]
hash = "sha1-dbdea6c7aa11112618e54b1686396a8f2cdf1887"
other = "Device not found."

["user_devices.verify.code_does_not_exist"]
hash = "sha1-f39569c8c6468178334afb410ca00ae9b8fb2cb7"
other = "The verification has expired, please resend the verification code."

["user_devices.verify.incorrect_code"]
hash = "sha1-a9b97a872d58db652376de7a6ce8012a9b333250"
other = "Incorrect verification code."

["user_devices.verify.too_much_try"]
hash = "sha1-d90b745bd03fd51e80e4ea0b8f2ac4967512b179"
other = "Too many attempts, please try again later to resend the verification code."

["xexcel.import.date"]
hash = "sha1-b7cada94d454ad864b9fe392eb658c6675525a09"
other = "行 %d: 列 [%s] 不是正確的日期格式。"

["xexcel.import.db_exist"]
hash = "sha1-f10f8f7ba45b1ee755138c804e800886c28ece8f"
other = "行 %d: 列 %s 不存在。"

["xexcel.import.incorrect_title"]
hash = "sha1-198ca0c8058747c3df44915caa5ecbae0311d811"
other = "匯入標題不正確。"

["xexcel.import.oneof"]
hash = "sha1-698b222319b1174e1fe026691fb6b2cefdee90c2"
other = "行 %d: 列 [%s] 必須是 '%s' 其中之一。"

["xexcel.import.required"]
hash = "sha1-536b31bd53fa880d9a0512a19c56dd4ac7bd3893"
other = "行 %d: 列 [%s] 不能為空。"
