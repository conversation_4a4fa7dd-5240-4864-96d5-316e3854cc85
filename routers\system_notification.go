package routers

import (
	v1 "github.com/Norray/medic-crew/api/v1"
	"github.com/gin-gonic/gin"
)

func systemNotificationRouter(Router *gin.RouterGroup, handlers ...gin.HandlerFunc) {
	r := Router.Group("/v1/app").Use(handlers...)
	{
		controller := v1.NewSystemNotificationController()
		r.GET("/system-notifications", controller.GetList)                              // 獲取用戶通知列表
		r.POST("/system-notifications/actions/mark-read", controller.MarkAsRead)        // 標記通知為已讀
		r.POST("/system-notifications/actions/mark-all-read", controller.MarkAllAsRead) // 標記所有通知為已讀
		r.POST("/system-notifications/actions/delete", controller.Delete)               // 刪除通知
		r.GET("/system-notifications/actions/unread-count", controller.GetUnreadCount)  // 獲取未讀通知數量
	}
}
