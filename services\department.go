package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var DepartmentService = new(departmentService)

type departmentService struct{}

func (s *departmentService) CheckIdExist(db *gorm.DB, m *model.Department, id uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.department.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	if err = db.Where("id = ? AND facility_id = ?", id, facilityId).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// CheckIsUsed 检查部门是否被使用
func (s *departmentService) CheckIsUsed(db *gorm.DB, departmentId uint64, facilityId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.department.is_used",
		Other: "This department is currently in use and cannot be deleted.",
	}
	var err error
	var count int64
	if err = db.Model(&model.FacilityUserDepartment{}).Where("department_id = ? AND facility_id = ?", departmentId, facilityId).Count(&count).Error; err != nil {
		return false, msg, err
	}
	if count > 0 {
		return true, msg, nil
	}
	return false, msg, nil
}

type DepartmentCreateReq struct {
	FacilityId          uint64 `json:"facilityId" binding:"required"`
	ServiceLocationId   uint64 `json:"serviceLocationId" binding:"required"`
	Name                string `json:"name" binding:"required"`
	Status              string `json:"status" binding:"required,oneof=ENABLE DISABLE"`
	AccessAllDepartment string `json:"accessAllDepartment" binding:"oneof=Y N"`
}

type DepartmentCreateResp struct {
	DepartmentId uint64 `json:"departmentId"` // 部門ID
}

func (s *departmentService) Create(db *gorm.DB, req DepartmentCreateReq) (DepartmentCreateResp, error) {
	var resp DepartmentCreateResp
	var err error
	var m model.Department
	_ = copier.Copy(&m, req)
	if err = db.Create(&m).Error; err != nil {
		return resp, err
	}
	resp.DepartmentId = m.Id
	return resp, nil
}

type DepartmentListReq struct {
	FacilityId uint64 `form:"facilityId" binding:"required"`
	Name       string `form:"name"`
	Status     string `form:"status"`
}

type DepartmentListResp struct {
	FacilityId          uint64 `json:"facilityId"`
	DepartmentId        uint64 `json:"departmentId"`
	ServiceLocationId   uint64 `json:"serviceLocationId"`
	ServiceLocationName string `json:"serviceLocationName"`
	Name                string `json:"name"`
	Status              string `json:"status"`
	AccessAllDepartment string `json:"accessAllDepartment"`
}

func (s *departmentService) List(db *gorm.DB, req DepartmentListReq, pageSet *xresp.PageSet) ([]DepartmentListResp, error) {
	var err error
	var resp []DepartmentListResp
	builder := db.Table("department AS d").
		Select([]string{
			"d.id AS department_id",
			"d.service_location_id",
			"sl.name AS service_location_name",
			"d.name",
			"d.status",
			"d.access_all_department",
		}).
		Joins("LEFT JOIN service_location AS sl ON d.service_location_id = sl.id").
		Where("d.facility_id = ?", req.FacilityId)
	if req.Name != "" {
		builder = builder.Where("d.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Status != "" {
		builder = builder.Where("d.status = ?", req.Status)
	}
	if err = builder.Order("d.id").Scopes(xresp.Paginate(pageSet)).Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type DepartmentSearchReq struct {
	FacilityId          uint64 `form:"facilityId" binding:"required"`
	Name                string `form:"name"`
	Status              string `form:"status"`
	AccessAllDepartment string `form:"accessAllDepartment"`
	SelectedId          uint64 `form:"selectedId"`
	Limit               int    `form:"limit"`
}

type DepartmentSearchResp struct {
	FacilityId          uint64 `json:"facilityId"`
	DepartmentId        uint64 `json:"departmentId"`
	ServiceLocationId   uint64 `json:"serviceLocationId"`
	ServiceLocationName string `json:"serviceLocationName"`
	Name                string `json:"name"`
	Status              string `json:"status"`
	AccessAllDepartment string `json:"accessAllDepartment"`
}

func (s *departmentService) Search(db *gorm.DB, req DepartmentSearchReq) ([]DepartmentSearchResp, error) {
	var err error
	var resp []DepartmentSearchResp
	builder := db.Table("department AS d").
		Select([]string{
			"d.facility_id",
			"d.id AS department_id",
			"d.service_location_id",
			"sl.name AS service_location_name",
			"d.name",
			"d.status",
			"d.access_all_department",
		}).
		Joins("LEFT JOIN service_location AS sl ON d.service_location_id = sl.id").
		Where("d.facility_id = ?", req.FacilityId)
	if req.Name != "" {
		builder = builder.Where("d.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Status != "" {
		builder = builder.Where("d.status = ?", req.Status)
	}
	if req.AccessAllDepartment != "" {
		builder = builder.Where("d.access_all_department = ?", req.AccessAllDepartment)
	}
	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(d.id = %d,0,1)", req.SelectedId))
	}
	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}
	if err = builder.Order("d.id").Find(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

type DepartmentEditReq struct {
	FacilityId          uint64 `json:"facilityId"  binding:"required"`
	DepartmentId        uint64 `json:"departmentId"  binding:"required"`
	ServiceLocationId   uint64 `json:"serviceLocationId"`
	Name                string `json:"name"`
	Status              string `json:"status"`
	AccessAllDepartment string `json:"accessAllDepartment"`
}

func (s *departmentService) Edit(db *gorm.DB, req DepartmentEditReq) error {
	var err error
	var m model.Department
	if err = db.Where("id = ? AND facility_id = ?", req.DepartmentId, req.FacilityId).First(&m).Error; err != nil {
		return err
	}
	_ = copier.Copy(&m, req)
	if err = db.Save(&m).Error; err != nil {
		return err
	}
	return nil
}

type DepartmentInquireReq struct {
	FacilityId   uint64 `form:"facilityId" binding:"required"`
	DepartmentId uint64 `form:"departmentId" binding:"required"`
}

type DepartmentInquireResp struct {
	FacilityId          uint64 `json:"facilityId"`
	DepartmentId        uint64 `json:"departmentId"`
	ServiceLocationId   uint64 `json:"serviceLocationId"`
	Name                string `json:"name"`
	Status              string `json:"status"`
	AccessAllDepartment string `json:"accessAllDepartment"`
}

func (s *departmentService) Inquire(db *gorm.DB, req DepartmentInquireReq) (DepartmentInquireResp, error) {
	var err error
	var resp DepartmentInquireResp
	var m model.Department
	if err = db.Where("id = ? AND facility_id = ?", req.DepartmentId, req.FacilityId).First(&m).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)
	resp.DepartmentId = m.Id
	return resp, nil
}

type DepartmentDeleteReq struct {
	FacilityId   uint64 `json:"facilityId"  binding:"required"`
	DepartmentId uint64 `json:"departmentId"  binding:"required"`
}

func (s *departmentService) Delete(db *gorm.DB, req DepartmentDeleteReq) error {
	var err error
	if err = db.Where("id = ? AND facility_id = ?", req.DepartmentId, req.FacilityId).Delete(&model.Department{}).Error; err != nil {
		return err
	}
	return nil
}
