package services

import (
	"strings"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

var MyService = new(myService)

type myService struct{}

// region ---------------------------------------------------- 用戶信息 ----------------------------------------------------

type BaseUserInfo struct {
	UserId    uint64 `json:"userId"`
	UserName  string `json:"userName"`
	UserType  string `json:"userType"`
	UserEmail string `json:"userEmail"`
}

type SystemUserInfo struct {
	BaseUserInfo
}

type FacilityUserInfo struct {
	BaseUserInfo
	FacilityId                      uint64 `json:"facilityId"`                      // 機構ID
	FacilityProfileId               uint64 `json:"facilityProfileId"`               // 機構資料ID
	LatestApprovedFacilityProfileId uint64 `json:"latestApprovedFacilityProfileId"` // 最新審核通過的機構資料ID
	LastFacilityAgreementId         uint64 `json:"lastFacilityAgreementId"`         // 機構協議ID
	LastSignedFacilityAgreementId   uint64 `json:"lastSignedFacilityAgreementId"`   // 機構已簽署協議ID
	FacilityType                    string `json:"facilityType"`                    // 機構類型
	FacilityName                    string `json:"facilityName"`                    // 機構名稱
	PrimaryUser                     string `json:"primaryUser"`                     // 機構主賬號
	Status                          string `json:"status"`                          // 機構資料狀態 PENDING=未提交, REVIEWING=審核中, APPROVED=已通過, DELETED=已刪除
}

type ProfessionalUserInfo struct {
	BaseUserInfo
	ProfessionalId              uint64 `json:"professionalId"`              // 專業人士ID
	ApprovedProfessionalId      uint64 `json:"approvedProfessionalId"`      // 審核通過的專業人士ID
	ApprovedProfessionalPhotoId uint64 `json:"approvedProfessionalPhotoId"` // 審核通過的專業人士照片ID

	FirstName        string `json:"firstName"`        // 名字
	LastName         string `json:"lastName"`         // 姓氏
	PermissionToWork string `json:"permissionToWork"` // 工作許可
	AbnEntityType    string `json:"abnEntityType"`    // ABN實體類型
	Status           string `json:"status"`           // 專業人士資料狀態 PENDING=未提交, REVIEWING=審核中, APPROVED=已通過, DEREGISTERED=已註銷, REJECTED=已拒絕
}

func (s *myService) MyUserInfo(db *gorm.DB, userId uint64) (interface{}, error) {
	var user xmodel.User
	if err := db.Where("id = ?", userId).First(&user).Error; err != nil {
		return nil, err
	}
	switch user.UserType {
	case model.UserUserTypeSuperAdmin, model.UserUserTypeSystemAdmin:
		return s.SystemUserInfo(user)
	case model.UserUserTypeFacilityUser:
		return s.FacilityUserInfo(db, user)
	case model.UserUserTypeProfessional:
		return s.ProfessionalUserInfo(db, user)
	}
	return nil, nil
}

func (s *myService) SystemUserInfo(user xmodel.User) (SystemUserInfo, error) {
	var systemUserInfo SystemUserInfo
	systemUserInfo.UserId = user.Id
	systemUserInfo.UserName = user.Name
	systemUserInfo.UserType = user.UserType
	systemUserInfo.UserEmail = user.Email

	return systemUserInfo, nil
}

func (s *myService) FacilityUserInfo(db *gorm.DB, user xmodel.User) (FacilityUserInfo, error) {
	var facilityUserInfo FacilityUserInfo
	facilityUserInfo.UserId = user.Id
	facilityUserInfo.UserName = user.Name
	facilityUserInfo.UserType = user.UserType
	facilityUserInfo.UserEmail = user.Email
	var facilityUser model.FacilityUser
	if err := db.Where("user_id = ?", user.Id).First(&facilityUser).Error; err != nil {
		return facilityUserInfo, err
	}
	var facilityProfile model.FacilityProfile
	// 顯示草稿版本
	if err := db.Model(&model.FacilityProfile{}).
		Where("facility_id = ?", facilityUser.FacilityId).
		Where("data_type = ?", model.FacilityProfileDataTypeDraft).
		First(&facilityProfile).Error; err != nil {
		return facilityUserInfo, err
	}
	facilityUserInfo.FacilityId = facilityProfile.FacilityId
	facilityUserInfo.FacilityName = facilityProfile.Name
	facilityUserInfo.Status = facilityProfile.Status
	facilityUserInfo.FacilityType = facilityProfile.FacilityType
	facilityUserInfo.FacilityProfileId = facilityProfile.Id

	// 獲取機構最新協議
	facilityAgreement, err := FacilityAgreementService.GetLastFacilityAgreement(db, facilityUser.FacilityId)
	if xgorm.IsSqlErr(err) {
		return facilityUserInfo, err
	}
	facilityUserInfo.LastFacilityAgreementId = facilityAgreement.Id

	// 獲取機構指定時間的已簽署協議
	facilityAgreement, err = FacilityAgreementService.GetLastFacilityAgreement(db, facilityUser.FacilityId, true)
	if xgorm.IsSqlErr(err) {
		return facilityUserInfo, err
	}
	facilityUserInfo.LastSignedFacilityAgreementId = facilityAgreement.Id

	var approvedFacilityProfile model.FacilityProfile
	// 顯示已通過版本
	if err := db.Model(&model.FacilityProfile{}).
		Where("facility_id = ?", facilityUser.FacilityId).
		Where("data_type = ?", model.FacilityProfileDataTypeApproved).
		First(&approvedFacilityProfile).Error; xgorm.IsSqlErr(err) {
		return facilityUserInfo, err
	}
	facilityUserInfo.LatestApprovedFacilityProfileId = approvedFacilityProfile.Id
	return facilityUserInfo, nil
}

func (s *myService) ProfessionalUserInfo(db *gorm.DB, user xmodel.User) (ProfessionalUserInfo, error) {
	var professionalUserInfo ProfessionalUserInfo
	professionalUserInfo.UserId = user.Id
	professionalUserInfo.UserName = user.Name
	professionalUserInfo.UserType = user.UserType
	professionalUserInfo.UserEmail = user.Email
	var professional model.Professional
	// 顯示草稿版本
	if err := db.Where("user_id = ?", user.Id).
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		First(&professional).Error; err != nil {
		return professionalUserInfo, err
	}
	professionalUserInfo.ProfessionalId = professional.Id
	professionalUserInfo.FirstName = professional.FirstName
	professionalUserInfo.LastName = professional.LastName
	professionalUserInfo.PermissionToWork = professional.PermissionToWork
	professionalUserInfo.Status = professional.Status
	if professional.RejectReason != "" {
		professionalUserInfo.Status = "REJECTED"
	}
	// 顯示審核通過版本
	var approvedProfessional model.Professional
	if err := db.Model(&model.Professional{}).
		Select("id, abn_entity_type").
		Where("user_id = ?", user.Id).
		Where("data_type = ?", model.ProfessionalDataTypeApproved).
		First(&approvedProfessional).Error; xgorm.IsSqlErr(err) {
		return professionalUserInfo, err
	}
	if approvedProfessional.Id > 0 {
		professionalUserInfo.ApprovedProfessionalId = approvedProfessional.Id
		professionalUserInfo.AbnEntityType = approvedProfessional.AbnEntityType
		if err := db.Table("professional_file_relation AS pfr").
			Joins("JOIN professional_file AS pf ON pf.id = pfr.professional_file_id AND pf.file_code = ?", model.ProfessionalFileCodePhoto).
			Where("pfr.professional_id = ?", professionalUserInfo.ApprovedProfessionalId).
			Pluck("pf.id", &professionalUserInfo.ApprovedProfessionalPhotoId).Error; err != nil {
			return professionalUserInfo, err
		}
	}

	return professionalUserInfo, nil
}

// endregion ---------------------------------------------------- 用戶信息 ----------------------------------------------------

// region ---------------------------------------------------- 修改個人密碼 ----------------------------------------------------

type ChangePasswordReq struct {
	OldPassword string `json:"oldPassword" binding:"required"` // 舊密碼
	NewPassword string `json:"newPassword" binding:"required"` // 新密碼
}

func (s *myService) ChangePassword(db *gorm.DB, userId uint64, req ChangePasswordReq) error {
	var err error
	pw := xtool.EncodeStringWithSalt(req.OldPassword, xconfig.AppConf.PasswordSalt)
	var user xmodel.User
	if err = db.Where("id = ?", userId).
		Where("password = ?", pw).
		Where("status = ?", "ENABLE").
		First(&user).Error; err != nil {
		return err
	}

	newPw := xtool.EncodeStringWithSalt(req.NewPassword, xconfig.AppConf.PasswordSalt)
	if err = db.Model(&xmodel.User{}).Where("id = ?", userId).Update("password", newPw).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 修改個人密碼 ----------------------------------------------------

type MyUserActionListResp struct {
	SelectedMenuResp
	Actions []SelectedActionResp `json:"actions" gorm:"-"`
}

type SelectedMenuResp struct {
	System string `json:"system"`
	Code   string `json:"code"`
}

type SelectedActionResp struct {
	Code string `json:"code"`
}

type roleActionListSelected struct {
	MenuId uint64 `json:"-"`
	Code   string `json:"code"`
}

func (s *myService) MyUserActionList(db *gorm.DB, userId uint64) ([]MyUserActionListResp, error) {
	var system string
	var err error

	resp := make([]MyUserActionListResp, 0)

	var user xmodel.User
	if err = db.Where("id = ?", userId).First(&user).Error; err != nil {
		return nil, err
	}

	var userRole xmodel.UserRole
	if err = db.Where("user_id = ?", userId).First(&userRole).Error; err != nil {
		return nil, err
	}

	switch user.UserType {
	case model.UserUserTypeSuperAdmin:
		system = "SYSTEM,PROGRAM"
	case model.UserUserTypeSystemAdmin:
		system = "SYSTEM"
	case model.UserUserTypeFacilityUser:
		system = "FACILITY"
	default:
		return resp, nil
	}

	var menus []*xmodel.Menu
	if err = db.
		Where("system IN (?)", strings.Split(system, ",")).
		Order("sort").
		Find(&menus).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}

	roleActionBuilder := db.Table("role_action").Select("id,action_id").Where("role_id = ? ", userRole.RoleId).Group("action_id")

	var actions []roleActionListSelected
	if err = db.
		Select([]string{
			"a.id AS action_id",
			"a.menu_id",
			"a.code",
		}).
		Table("action a").
		Joins("JOIN menu m ON m.id = a.menu_id").
		Joins("JOIN (?) ra ON ra.action_id = a.id", roleActionBuilder).
		Where("m.system in (?)", strings.Split(system, ",")).
		Order("a.menu_id").
		Order("a.sort").
		Find(&actions).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	actionsMap := s.RebuildSelectedActionsToMap(actions)

	for _, menu := range menus {
		var m SelectedMenuResp
		_ = copier.Copy(&m, &menu)
		ma := MyUserActionListResp{
			SelectedMenuResp: m,
			Actions:          make([]SelectedActionResp, 0),
		}
		as, ok := actionsMap[menu.Id]
		if ok {
			ma.Actions = as
		}
		sel := false
		if len(ma.Actions) > 0 {
			sel = true
		}
		if sel {
			resp = append(resp, ma)
		}
	}

	return resp, nil
}

func (s *myService) RebuildSelectedActionsToMap(actions []roleActionListSelected) map[uint64][]SelectedActionResp {
	result := make(map[uint64][]SelectedActionResp)
	for _, action := range actions {
		arr, ok := result[action.MenuId]
		var a SelectedActionResp
		_ = copier.Copy(&a, &action)
		if ok {
			arr = append(arr, a)
			result[action.MenuId] = arr
		} else {
			result[action.MenuId] = []SelectedActionResp{a}
		}
	}
	return result
}
