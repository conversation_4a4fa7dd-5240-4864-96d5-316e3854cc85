package services

import (
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/jinzhu/now"
	uuid "github.com/satori/go.uuid"

	"bytes"
	"encoding/json"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var ProfessionalProfileService = new(professionalProfileService)

type professionalProfileService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

func (s *professionalProfileService) CheckIdExist(db *gorm.DB, m *model.Professional, id uint64, userId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	builder := db.Model(&m)
	if len(userId) > 0 {
		builder = builder.Where("user_id = ?", userId[0])
	}
	if err = builder.Where("id = ?", id).First(&m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查專業人士是否已初始化
func (s *professionalProfileService) CheckAlreadyInit(db *gorm.DB, m *model.Professional, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.already_init",
		Other: "Some thing went wrong, please try again later.",
	}
	var err error
	if err = db.Where("id = ?", id).Where("data_type = ?", model.ProfessionalDataTypeDraft).First(m).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查是否可以編輯資料
func (s *professionalProfileService) CheckCanEdit(professional model.Professional) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.can_edit",
		Other: "The current status cannot be edited.",
	}
	if professional.Id == 0 {
		return false, msg, nil
	}

	// 如果專業人士狀態為未提交或已通過，則可以編輯
	if professional.Status == model.ProfessionalStatusPending || professional.Status == model.ProfessionalStatusApproved {
		return true, msg, nil
	}
	return false, msg, nil
}

// 檢查首選專業是否有效, 最多15個, 且必須在專業的細分組中
func (s *professionalProfileService) CheckPreferredSpecialities(db *gorm.DB, profession string, preferredSpecialities string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.preferred_specialities",
		Other: "The preferred specialities is not valid.",
	}
	preferredSpecialityArray := strings.Split(preferredSpecialities, ",")
	if len(preferredSpecialityArray) == 0 || preferredSpecialities == "" {
		return true, msg, nil
	} else if len(preferredSpecialityArray) > 15 {
		return false, msg, nil
	}

	specialities, err := s.GetProfessionSpecialities(db, profession)
	if err != nil {
		return false, msg, err
	}
	var specialitiesMap = map[string]bool{}
	for _, speciality := range specialities {
		specialitiesMap[speciality] = true
	}

	for _, speciality := range preferredSpecialityArray {
		if !specialitiesMap[speciality] {
			return false, msg, nil
		}
	}

	return true, msg, nil
}

// 獲取專業的細分項
func (s *professionalProfileService) GetProfessionSpecialities(db *gorm.DB, profession string) ([]string, error) {
	var specialities []string
	switch profession {
	case model.ProfessionalProfessionMedicalPractitioner:
		// 細分組
		var subGroups []string
		if err := db.Model(&model.Selection{}).
			Where("selection_type = ?", model.SelectionTypePreferredSpecialityMedicalPractitioner).
			Pluck("code", &subGroups).Error; err != nil {
			return nil, err
		}
		if len(subGroups) == 0 {
			return specialities, nil
		}
		if err := db.Model(&model.Selection{}).
			Where("selection_type IN (?)", subGroups).
			Pluck("code", &specialities).Error; err != nil {
			return nil, err
		}
	case model.ProfessionalProfessionEnrolledNurse,
		model.ProfessionalProfessionRegisteredNurse,
		model.ProfessionalProfessionPersonalCareWorker:
		var selectionType string
		if model.ProfessionalProfessionPersonalCareWorker == profession {
			selectionType = model.SelectionTypePreferredSpecialityPersonalCareWorker
		} else {
			selectionType = model.SelectionTypePreferredSpecialityNurse
		}
		if err := db.Model(&model.Selection{}).
			Where("selection_type = ?", selectionType).
			Pluck("code", &specialities).Error; err != nil {
			return nil, err
		}
	}
	return specialities, nil
}

// 檢查是否可以提交審核
func (s *professionalProfileService) CheckCanSubmit(db *gorm.DB, professional model.Professional, updatePrompt string, onlyCheck bool) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.can_submit",
		Other: "The profile is not complete, please fill in completely.",
	}

	// 檢查是否已經提交過
	if professional.Status != model.ProfessionalStatusPending {
		statusMsg := i18n.Message{
			ID:    "checker.professional.already_submitted",
			Other: "The profile has already been submitted.",
		}
		return false, statusMsg, nil
	}

	// 查詢進度
	progress, err := s.ProfessionalProfileProgress(db, ProfessionalProfileProgressReq{
		ProfessionalId: professional.Id,
	})
	if err != nil {
		return false, msg, err
	}
	if progress.Progress < 100 {
		return false, msg, nil
	}

	// 只是檢查，不需要檢查修改說明
	if onlyCheck {
		return true, i18n.Message{}, nil
	}

	// 如果不是第一次提交，需要填寫修改提示
	var approvedCount int64
	if err := db.Model(&model.Professional{}).
		Where("data_type = ?", model.ProfessionalDataTypeApproved).
		Where("status = ?", model.ProfessionalStatusApproved).
		Where("user_id = ?", professional.UserId).
		Count(&approvedCount).Error; err != nil {
		return false, msg, err
	}
	if approvedCount > 0 && updatePrompt == "" {
		missingMsg := i18n.Message{
			ID:    "checker.professional.missing_update_prompt",
			Other: "Please fill in the update prompt.",
		}
		return false, missingMsg, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查是否可以撤銷審核
func (s *professionalProfileService) CheckCanWithdraw(professional model.Professional) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.can_withdraw",
		Other: "The profile cannot be withdrawn.",
	}

	// 檢查是否在審核中
	if professional.Status != model.ProfessionalStatusReviewing {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// 檢查Abn是否有效
func (s *professionalProfileService) CheckAbn(db *gorm.DB, abnNumber string, abnInfo *AbnInfo, abnResp *AbnDetailQueryResp) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.abn_invalid",
		Other: "The ABN is invalid.",
	}
	var err error
	var resp AbnDetailQueryResp
	resp, err = AbnService.GetAbnInfoByApi(db, abnNumber)

	if err != nil {
		if errors.Is(err, ABNNotFoundError) {
			return false, msg, nil
		}
		return false, msg, err
	}
	*abnInfo = AbnService.getAbnInfo(resp)
	if abnInfo.Status == AbnStatusCancelled {
		cancelledMsg := i18n.Message{
			ID:    "checker.professional.abn_cancelled",
			Other: "The ABN is cancelled.",
		}
		return false, cancelledMsg, nil
	}
	abnResp = &resp

	return true, i18n.Message{}, nil
}
func (s *professionalProfileService) CheckCanApprove(professional model.Professional, approve string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.can_not_approve",
		Other: "Some thing went wrong, please try again later.",
	}
	if professional.Id == 0 || professional.DataType != model.ProfessionalDataTypeDraft || professional.Status != model.ProfessionalStatusReviewing {
		return false, msg, nil
	}

	if approve == "Y" {
		profile, err := professional.UnmarshalProfile(professional.ProfileJson)
		if err != nil {
			return false, msg, err
		}
		for _, reference := range profile.References {
			if reference.FormStatus != model.ProfessionalProfileReferencesFormStatusApproved {
				return false, i18n.Message{
					ID:    "checker.professional.references_pending_approval",
					Other: "Referee information pending approval. Please review and approve the referee details first.",
				}, nil
			}
		}
	}

	return true, msg, nil
}
func (s *professionalProfileService) CheckMedicalPractitionerExperienceLevel(db *gorm.DB, experienceLevel string, graduationYear int) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.experience_level_invalid",
		Other: "Experience level does not match qualification end date.",
	}
	selection, err := SelectionService.FindByCode(db, experienceLevel)
	if err != nil {
		return false, msg, err
	}
	if selection == nil {
		return false, msg, nil
	}
	level := 0
	// PGY1 取數字
	level, err = strconv.Atoi(selection.Code[3:4])
	if err != nil {
		return false, msg, err
	}

	nowTime := time.Now()
	diffYear := nowTime.Year() - graduationYear
	if diffYear < level {
		return false, msg, nil
	}

	return true, msg, nil
}

// 檢查Medical Practitioner的Preferred Grade是否符合Experience Level要求
func (s *professionalProfileService) CheckMedicalPractitionerPreferredGrade(experienceLevel string, preferredGrade string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.preferred_grade_invalid",
		Other: "Preferred Grade does not match Experience Level.",
	}

	// 如果不是Medical Practitioner相關的Experience Level，直接通過
	if !strings.HasPrefix(experienceLevel, "PGY") {
		return true, i18n.Message{}, nil
	}

	// 定義每個Experience Level允許的Preferred Grade選項
	allowedGrades := map[string][]string{
		"PGY2": {"RESIDENT_MEDICAL_OFFICER"},
		"PGY3": {"RESIDENT_MEDICAL_OFFICER", "REGISTRAR_UNACCREDITED"},
		"PGY4": {"RESIDENT_MEDICAL_OFFICER", "REGISTRAR_UNACCREDITED", "REGISTRAR_ACCREDITED"},
		"PGY5": {"RESIDENT_MEDICAL_OFFICER", "REGISTRAR_UNACCREDITED", "REGISTRAR_ACCREDITED", "FELLOW"},
		"PGY6": {"RESIDENT_MEDICAL_OFFICER", "REGISTRAR_UNACCREDITED", "REGISTRAR_ACCREDITED", "FELLOW", "SPECIALIST"},
		"PGY7": {"RESIDENT_MEDICAL_OFFICER", "REGISTRAR_UNACCREDITED", "REGISTRAR_ACCREDITED", "FELLOW", "SPECIALIST"},
		"PGY8": {"RESIDENT_MEDICAL_OFFICER", "REGISTRAR_UNACCREDITED", "REGISTRAR_ACCREDITED", "FELLOW", "SPECIALIST"},
	}

	// 檢查當前Experience Level是否有對應的允許選項
	grades, exists := allowedGrades[experienceLevel]
	if !exists {
		return false, msg, nil
	}

	// 檢查Preferred Grade是否在允許的選項中
	for _, allowedGrade := range grades {
		if preferredGrade == allowedGrade {
			return true, i18n.Message{}, nil
		}
	}

	return false, msg, nil
}

// 根據Permission to Work獲取應該禁用的文件類型
func (s *professionalProfileService) GetDisabledFileTypesByPermissionToWork(permissionToWork string) []string {
	switch permissionToWork {
	case model.ProfessionalPermissionToWorkAustralianCitizen:
		// Australian Citizen: 禁用foreign passport
		return []string{model.ProfessionalFileCodeForeignPassport}
	case model.ProfessionalPermissionToWorkPermanentResident:
		// Permanent Resident: 禁用australian passport和australian citizenship certificate
		return []string{
			model.ProfessionalFileCodeAustralianPassport,
			model.ProfessionalFileCodeAustralianCitizenshipCertificate,
		}
	case model.ProfessionalPermissionToWorkVisa:
		// Visa With Work Rights: 禁用australian passport和australian citizenship certificate
		return []string{
			model.ProfessionalFileCodeAustralianPassport,
			model.ProfessionalFileCodeAustralianCitizenshipCertificate,
		}
	default:
		return []string{}
	}
}

// 檢查IdCheckFileTypes中的文件類型是否在提交的文件中存在
func (s *professionalProfileService) CheckIdCheckFileTypes(professional model.Professional) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.id_check_file_types_invalid",
		Other: "ID Check file types do not match uploaded files.",
	}
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return false, i18n.Message{}, err
	}
	idCheckFileTypes := profile.IdCheckFileTypes

	// 獲取所有提交的文件類型
	if idCheckFileTypes == "" {
		return true, i18n.Message{}, nil
	}
	var fileTypeMap = make(map[string]bool)
	for _, file := range profile.Files {
		if len(file.ProfessionalFileIds) > 0 {
			fileTypeMap[file.FileCode] = true
		}
	}

	// 檢查每個已審核的文件類型是否在提交的文件中存在
	verifiedTypes := strings.Split(idCheckFileTypes, ",")
	for _, verifiedType := range verifiedTypes {
		if !fileTypeMap[verifiedType] {
			return false, msg, nil
		}
	}

	return true, i18n.Message{}, nil
}

// 檢查所有文件的過期時間
func (s *professionalProfileService) CheckFileExpiration(lang string, professional model.Professional) (bool, string, error) {
	msgInvalid := i18n.Message{
		ID:    "checker.professional.file_expiration_invalid",
		Other: "{{.Name}} expiration date is invalid.",
	}
	msgToday := i18n.Message{
		ID:    "checker.professional.file_expiration_invalid.today",
		Other: "{{.Name}} has expired",
	}
	msgPassport := i18n.Message{
		ID:    "checker.professional.file_expiration_invalid.passport",
		Other: "The passport must be valid or expired within 2 years.",
	}
	msgCriminal := i18n.Message{
		ID:    "checker.professional.file_expiration_invalid.criminal",
		Other: "The National Criminal Check Expiry Date must be at least 3 years ago.",
	}
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return false, "", err
	}

	today := now.With(time.Now()).BeginningOfDay()
	for _, file := range profile.Files {
		if needParam, ok := model.ProfessionalProfileFileNeedParamMap[file.FileCode]; !ok {
			return false, "", fmt.Errorf("file code not find: %s", file.FileCode)
		} else if needParam&model.ProfessionalProfileFileNeedExpiryDate != 0 {
			if file.ExpiryDate == "" {
				fileName := model.ProfessionalProfileFileNameMap[file.FileCode]
				msg := xi18n.LocalizeWithLangAndTemplateData(lang, &msgInvalid, map[string]string{
					"Name": xi18n.LocalizeWithLang(lang, &fileName),
				})
				return false, msg, nil
			}
			// 如果需要過期日期，檢查是否過期
			expiryDate, err := time.ParseInLocation(xtool.DateDayA, file.ExpiryDate, time.UTC)
			if err != nil {
				fileName := model.ProfessionalProfileFileNameMap[file.FileCode]
				msg := xi18n.LocalizeWithLangAndTemplateData(lang, &msgInvalid, map[string]string{
					"Name": xi18n.LocalizeWithLang(lang, &fileName),
				})
				return false, msg, nil
			}
			switch file.FileCode {
			case model.ProfessionalFileCodeAustralianPassport:
				// 兩年前的今天(日月一樣，年份減2)
				twoYearsAgo := today.AddDate(-2, 0, 0)
				if expiryDate.Before(twoYearsAgo) {
					return false, xi18n.LocalizeWithLang(lang, &msgPassport), nil
				}
			case model.ProfessionalFileCodeNationalCriminalCheck:
				// 三年前的今天
				threeYearsAgo := today.AddDate(-3, 0, 0)
				if expiryDate.Before(threeYearsAgo) {
					return false, xi18n.LocalizeWithLang(lang, &msgCriminal), nil
				}
			default:
				// 其他文件類型，不做特別處理
				if expiryDate.Before(today) {
					fileName := model.ProfessionalProfileFileNameMap[file.FileCode]
					msg := xi18n.LocalizeWithLangAndTemplateData(lang, &msgToday, map[string]string{
						"Name": xi18n.LocalizeWithLang(lang, &fileName),
					})
					return false, msg, nil
				}
			}
		}
	}
	return true, "", nil
}

// 檢查WorkingWithChildrenOrVulnerablePeopleStates中的地區是否與文件描述匹配
func (s *professionalProfileService) CheckWorkingWithChildrenOrVulnerablePeopleStates(professional model.Professional) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.professional.working_with_children_states_mismatch",
		Other: "Working with children/vulnerable people states do not match file descriptions.",
	}

	// 解析專業人士資料
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return false, msg, err
	}

	// 如果沒有設置地區，直接通過
	if profile.WorkingWithChildrenOrVulnerablePeopleStates == "" {
		return true, i18n.Message{}, nil
	}

	// 解析設置的地區列表
	statesList := strings.Split(profile.WorkingWithChildrenOrVulnerablePeopleStates, ",")
	requiredStatesMap := make(map[string]bool)
	for _, state := range statesList {
		state = strings.TrimSpace(state)
		if state != "" {
			requiredStatesMap[state] = false
		}
	}

	// 如果沒有有效的地區，直接通過
	if len(requiredStatesMap) == 0 {
		return true, i18n.Message{}, nil
	}

	// 查找WorkingWithChildrenOrVulnerablePeople文件
	var workingWithChildrenFiles []model.ProfessionalProfileFile
	for _, file := range profile.Files {
		if file.FileCode == model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople {
			if _, ok := requiredStatesMap[file.Description]; ok {
				workingWithChildrenFiles = append(workingWithChildrenFiles, file)
			}
		}
	}

	// 如果沒有相關文件，檢查失敗
	if len(workingWithChildrenFiles) == 0 {
		return false, msg, nil
	}

	// 收集文件描述中的地區
	fileStatesMap := make(map[string]bool)
	for _, file := range workingWithChildrenFiles {
		if file.Description != "" {
			// 解析文件描述中的地區（假設格式為逗號分隔）
			descriptionStates := strings.Split(file.Description, ",")
			for _, state := range descriptionStates {
				state = strings.TrimSpace(state)
				if state != "" {
					fileStatesMap[state] = true
				}
			}
		}
	}

	// 檢查所有設置的地區是否都在文件描述中存在
	for requiredState := range requiredStatesMap {
		if !fileStatesMap[requiredState] {
			return false, msg, nil
		}
	}

	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 初始化 ----------------------------------------------------

type ProfessionalProfileInitReq struct {
	ProfessionalId   uint64 `json:"-"`
	FirstName        string `json:"firstName" binding:"required,max=128"`
	LastName         string `json:"lastName" binding:"required,max=128"`
	Phone            string `json:"phone" binding:"required,max=20"`
	PermissionToWork string `json:"permissionToWork" binding:"required,max=255"`
}

func (s *professionalProfileService) Init(db *gorm.DB, req ProfessionalProfileInitReq) error {
	var err error
	var professional model.Professional
	if err = db.Where("id = ?", req.ProfessionalId).First(&professional).Error; err != nil {
		return err
	}
	updateMap := map[string]interface{}{
		"first_name":         req.FirstName,
		"last_name":          req.LastName,
		"permission_to_work": req.PermissionToWork,
	}
	if err := db.Model(&professional).Updates(updateMap).Error; err != nil {
		return err
	}
	var user xmodel.User
	if err = db.Where("id = ?", professional.UserId).First(&user).Error; err != nil {
		return err
	}
	updateMap = map[string]interface{}{
		"phone": req.Phone,
	}
	if err = db.Model(&user).Updates(updateMap).Error; err != nil {
		return err
	}
	// 創建默認的養老金記錄
	superannuations := []model.ProfessionalSuperannuation{
		{
			UserId:               professional.UserId,
			DataType:             model.ProfessionalSuperannuationDataTypeDraft,
			FullName:             req.FirstName + " " + req.LastName,
			DeclarationConfirmed: "N",
		},
		{
			UserId:               professional.UserId,
			DataType:             model.ProfessionalSuperannuationDataTypeSubmitted,
			FullName:             req.FirstName + " " + req.LastName,
			DeclarationConfirmed: "N",
		},
	}
	if err = db.CreateInBatches(superannuations, 2).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 初始化 ----------------------------------------------------

// region ---------------------------------------------------- 獲取專業人士資料 ----------------------------------------------------

func (s *professionalProfileService) GetProfessionalProfile(db *gorm.DB, professionalId uint64) (model.Professional, model.ProfessionalProfile, error) {
	var err error
	var professional model.Professional
	var profile model.ProfessionalProfile
	if err = db.Where("id = ?", professionalId).First(&professional).Error; err != nil {
		return professional, profile, err
	}
	profile, err = professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return professional, profile, err
	}
	return professional, profile, nil
}

// endregion ---------------------------------------------------- 獲取專業人士資料 ----------------------------------------------------

// region ---------------------------------------------------- 詳情 ----------------------------------------------------

type ProfessionalProfileInquireReq struct {
	ProfessionalId uint64 `form:"professionalId" binding:"required"`
}

type ProfessionalProfileInquireResp struct {
	ProfessionalId                              uint64                                  `json:"professionalId"`
	UserId                                      uint64                                  `json:"userId"`                                              // 用戶Id
	DataType                                    string                                  `json:"dataType"`                                            // 數據類型 DRAFT=草稿,APPROVED=已通過,HISTORY=歷史
	FirstName                                   string                                  `json:"firstName"`                                           // 名字
	LastName                                    string                                  `json:"lastName"`                                            // 姓氏
	MinimumHourlyRate                           decimal.Decimal                         `json:"minimumHourlyRate"`                                   // 最低時薪
	PreferredState                              string                                  `json:"preferredState"`                                      // 首選州
	PreferredLocality                           string                                  `json:"preferredLocality"`                                   // 首選地區
	DistanceWithin                              decimal.Decimal                         `json:"distanceWithin"`                                      // 距離範圍(KM)
	PermissionToWork                            string                                  `json:"permissionToWork"`                                    // 工作許可
	Profession                                  string                                  `json:"profession"`                                          // 專業
	Language                                    string                                  `json:"language"`                                            // 語言能力(逗號分隔)
	MedicationEndorsement                       string                                  `json:"medicationEndorsement"`                               // 藥物授權 Y/N (僅Enrolled Nurse)
	BankStateBranch                             string                                  `json:"bankStateBranch"`                                     // 銀行分行(BSB)
	BankAccountNumber                           string                                  `json:"bankAccountNumber"`                                   // 銀行帳戶號碼
	BankAccountName                             string                                  `json:"bankAccountName"`                                     // 銀行帳戶名稱
	Gender                                      string                                  `json:"gender"`                                              // 性別
	DateOfBirth                                 string                                  `json:"dateOfBirth"`                                         // 出生日期
	Address                                     string                                  `json:"address"`                                             // 居住地址
	AddressExtra                                string                                  `json:"addressExtra"`                                        // 居住地址補充
	LocationState                               string                                  `json:"locationState"`                                       // 州
	LocationCity                                string                                  `json:"locationCity"`                                        // 城市
	LocationRoute                               string                                  `json:"locationRoute"`                                       // 街道
	LocationLat                                 decimal.Decimal                         `json:"locationLat"`                                         // 緯度
	LocationLng                                 decimal.Decimal                         `json:"locationLng"`                                         // 經度
	ExperienceLevel                             string                                  `json:"experienceLevel"`                                     // 經驗級別
	PreferredSpecialities                       []model.ProfessionalPreferredSpeciality `json:"preferredSpecialities"`                               // 首選專業
	SoleTrader                                  string                                  `json:"soleTrader"`                                          // 是否為個人獨資 Y/N
	AhpraNumber                                 string                                  `json:"ahpraNumber"`                                         // AHPRA註冊號碼
	AhpraExpiryDate                             string                                  `swaggertype:"string" json:"ahpraExpiryDate"`                // AHPRA註冊到期日
	AbnNumber                                   string                                  `json:"abnNumber"`                                           // ABN號碼
	AbnEntityName                               string                                  `json:"-"`                                                   // ABN實體名稱
	AbnEntityType                               string                                  `json:"abnEntityType"`                                       // ABN實體類型
	AbnMatched                                  string                                  `json:"abnMatched" gorm:"-"`                                 // ABN名稱是否匹配
	AbnValid                                    string                                  `json:"abnValid"`                                            // ABN有效 Y/N
	IdCheckExpiryDate                           string                                  `swaggertype:"string" json:"idCheckExpiryDate"`              // 身份證明到期日
	IdCheckDeletionConsent                      string                                  `json:"idCheckDeletionConsent"`                              // 用戶是否同意ID文件刪除聲明 Y/N
	IdCheckFileTypes                            string                                  `json:"idCheckFileTypes"`                                    // 已審核的ID文件類型（逗號分隔）
	WorkingWithChildrenOrVulnerablePeopleStates string                                  `json:"workingWithChildrenOrVulnerablePeopleSelectedStates"` // 有兒童/脆弱人群工作資格的地區
	SignedAgreement                             string                                  `json:"signedAgreement"`                                     // 是否簽署協議 Y/N
	ReferenceFormStatus                         string                                  `json:"referenceFormStatus"`                                 // FILLED = 已全部填寫, APPROVED= 已全部通過, PENDING = 未全部填寫
	ReferencesTermsConfirm                      string                                  `json:"referencesTermsConfirm"`                              // 推薦人協議確認
	ApplicationTime                             *time.Time                              `json:"applicationTime"`                                     // 提交審批時間
	ApprovedUserId                              uint64                                  `json:"approvedUserId"`                                      // 審核人員
	ApprovedTime                                *time.Time                              `swaggertype:"string" json:"approvedTime"`                   // 審核時間
	RejectReason                                string                                  `json:"rejectReason"`                                        // 拒絕原因
	Status                                      string                                  `json:"status"`                                              // 狀態
	UpdatePrompt                                string                                  `json:"updatePrompt"`                                        // 更新提示
	ApprovedProfessionalId                      uint64                                  `json:"approvedProfessionalId" gorm:"-"`                     // 審核通過的專業人士ID

	Version string `json:"version"` // JSON結構版本

	EmergencyContactFirstName    string `json:"emergencyContactFirstName"`    // 緊急聯絡人名字
	EmergencyContactLastName     string `json:"emergencyContactLastName"`     // 緊急聯絡人姓氏
	EmergencyContactPhone        string `json:"emergencyContactPhone"`        // 緊急聯絡人電話
	EmergencyContactRelationship string `json:"emergencyContactRelationship"` // 緊急聯絡人關係

	Experiences                      []model.ProfessionalExperience `json:"experiences"`                                                    // 工作經驗
	CvNoContactDetailsConfirmed      string                         `json:"cvNoContactDetailsConfirmed"`                                    // 履歷是否確認無聯絡人詳情 Y/N
	CompletedStudiesInLastThreeYears string                         `json:"completedStudiesInLastThreeYears" binding:"omitempty,oneof=Y N"` // 過去三年內完成學習 Y/N
	Qualification                    string                         `json:"qualification"`                                                  // 學歷
	QualificationEndDate             string                         `json:"qualificationEndDate" binding:"omitempty,datetime=2006-01-02"`   // 學歷結束日期(YYYY-MM-DD)

	QualificationName                    string                                   `json:"qualificationName"`                                                  // 學歷名稱
	GraduationInstitution                string                                   `json:"graduationInstitution"`                                              // 畢業院校
	InstitutionCountry                   string                                   `json:"institutionCountry"`                                                 // 院校國家
	GraduationYear                       int32                                    `json:"graduationYear"`                                                     // 畢業年份(yyyy)
	References                           []ProfessionalReference                  `json:"references"`                                                         // 推薦人
	HasOverseasCitizenshipOrPr           string                                   `json:"hasOverseasCitizenshipOrPr" binding:"omitempty,oneof=Y N"`           // 是否擁有海外公民身份或永久居留權 Y/N
	RequiresStatutoryDeclaration         string                                   `json:"requiresStatutoryDeclaration" binding:"omitempty,oneof=Y N"`         // 是否需要法定聲明 Y/N
	HasCompletedInfectionControlTraining string                                   `json:"hasCompletedInfectionControlTraining" binding:"omitempty,oneof=Y N"` // 是否完成感染控制培訓 Y/N
	DisclosureQuestions                  []model.ProfessionalDisclosureQuestion   `json:"disclosureQuestions"`                                                // 披露問題
	Files                                map[string][]ProfessionalProfileFileInfo `json:"files" gorm:"-"`                                                     // 文件
}

type ProfessionalReference struct {
	ReferenceFirstName      string `json:"referenceFirstName"`      // 推薦人名字
	ReferenceLastName       string `json:"referenceLastName"`       // 推薦人姓氏
	ReferenceEmail          string `json:"referenceEmail"`          // 推薦人電郵
	ReferenceFacility       string `json:"referenceFacility"`       // 推薦人機構
	ReferenceRole           string `json:"referenceRole"`           // 推薦人職位
	DateWorkedTogetherBegin string `json:"dateWorkedTogetherBegin"` // 一起工作的開始時間
	DateWorkedTogetherEnd   string `json:"dateWorkedTogetherEnd"`   // 一起工作的結束時間，如果是空，說明是現在還在持續
	FormUuid                string `json:"formUuid"`                // 推薦表唯一碼
	FormStatus              string `json:"formStatus"`              // 推薦表狀態 系統決定 APPROVED NEED_APPROVED PENDING REJECTED
}

type ProfessionalPreferredSpecialityDetail struct {
	Speciality        string `json:"speciality" `        // 首選專業
	SpecialityName    string `json:"specialityName" `    // 首選專業名稱
	SubSpeciality     string `json:"subSpeciality" `     // 首選子專業
	SubSpecialityName string `json:"subSpecialityName" ` // 首選子專業名稱
	Grade             string `json:"grade"`              // 首選級別
	GradeName         string `json:"gradeName"`          // 首選級別名稱
}
type ProfessionalProfileFileInfo struct {
	FileCode            string                          `json:"fileCode"`              // 文件編號
	ProfessionalFiles   []ProfessionalProfileFileDetail `json:"professionalFiles"`     // 文件信息
	ProfessionalFileIds []uint64                        `json:"professionalFileIds"`   // 文件信息
	ExpiryDate          string                          `json:"expiryDate,omitempty"`  // 到期日
	Description         string                          `json:"description,omitempty"` // 描述
	Number              string                          `json:"number,omitempty"`      // 編號
}
type ProfessionalProfileFileDetail struct {
	ProfessionalFileId uint64 `json:"professionalFileId"` // 文件ID
	Filename           string `json:"filename"`           // 文件名稱
	UuidName           string `json:"-"`                  // UUID文件名稱(內部使用)
}

func (s *professionalProfileService) Inquire(db *gorm.DB, req ProfessionalProfileInquireReq) (ProfessionalProfileInquireResp, error) {
	var err error
	var resp ProfessionalProfileInquireResp
	professional, profile, err := s.GetProfessionalProfile(db, req.ProfessionalId)
	if err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, professional)
	resp.ProfessionalId = professional.Id
	_ = copier.Copy(&resp, profile)

	var fileDetails []ProfessionalProfileFileDetail
	if err = db.Table("professional_file AS pf").
		Joins("JOIN professional_file_relation AS pfr ON pfr.professional_file_id = pf.id").
		Joins("JOIN professional AS p ON p.user_id = pf.user_id").
		Where("p.id = ?", req.ProfessionalId).
		Select([]string{
			"pf.id AS professional_file_id",
			"pf.origin_file_name AS filename",
			"pf.file_name AS uuid_name",
		}).
		Scan(&fileDetails).Error; err != nil {
		return resp, err
	}
	fileMap := make(map[uint64]ProfessionalProfileFileDetail)
	for _, file := range fileDetails {
		fileMap[file.ProfessionalFileId] = file
	}

	// 文件
	files := make(map[string][]ProfessionalProfileFileInfo)
	for _, file := range profile.Files {
		if _, ok := files[file.FileCode]; !ok {
			files[file.FileCode] = []ProfessionalProfileFileInfo{}
		}
		var details []ProfessionalProfileFileDetail
		for _, id := range file.ProfessionalFileIds {
			if detail, ok := fileMap[id]; ok {
				details = append(details, detail)
			}
		}

		files[file.FileCode] = append(files[file.FileCode], ProfessionalProfileFileInfo{
			FileCode:            file.FileCode,
			ProfessionalFiles:   details,
			ProfessionalFileIds: file.ProfessionalFileIds,
			ExpiryDate:          file.ExpiryDate,
			Description:         file.Description,
			Number:              file.Number,
		})
	}
	resp.Files = files

	// 銀行賬戶
	var professionalBankAccount model.ProfessionalBankAccount
	if err = db.Where("user_id = ?", professional.UserId).First(&professionalBankAccount).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	resp.BankStateBranch = professionalBankAccount.BankStateBranch
	resp.BankAccountNumber = professionalBankAccount.BankAccountNumber
	resp.BankAccountName = professionalBankAccount.BankAccountName

	// ABN實體名稱是否匹配
	resp.AbnMatched = "N"
	if resp.AbnNumber != "" {
		if ok := s.MatchAbnEntityName(resp.AbnEntityName, resp.FirstName, resp.LastName); ok {
			resp.AbnMatched = "Y"
		}
	}

	// 如果是審核中，查詢有無更新附加證明
	if professional.Status == model.ProfessionalStatusReviewing {
		var fileIds []uint64
		if err = db.Model(&model.ProfessionalFile{}).
			Table("professional_file AS pf").
			Joins("JOIN professional_file_relation AS pfr ON pfr.professional_file_id = pf.id").
			Where("pfr.professional_id = ?", professional.Id).
			Where("pf.file_code = ?", model.ProfessionalFileCodeUpdatePrompt).
			Pluck("pf.id", &fileIds).Error; err != nil {
			return resp, err
		}
		if len(fileIds) > 0 {
			var professionalFiles []ProfessionalProfileFileDetail
			for _, id := range fileIds {
				if detail, ok := fileMap[id]; ok {
					professionalFiles = append(professionalFiles, detail)
				}
			}

			files[model.ProfessionalFileCodeUpdatePrompt] = []ProfessionalProfileFileInfo{
				{
					FileCode:          model.ProfessionalFileCodeUpdatePrompt,
					ProfessionalFiles: professionalFiles,
				},
			}
		}
	}

	if professional.DataType == model.ProfessionalDataTypeDraft {
		// 顯示審核通過版本
		if err = db.Model(&model.Professional{}).
			Where("user_id = ?", professional.UserId).
			Where("data_type = ?", model.ProfessionalDataTypeApproved).
			Pluck("id", &resp.ApprovedProfessionalId).Error; err != nil {
			return resp, err
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 詳情 ----------------------------------------------------

// region ---------------------------------------------------- 編輯 ----------------------------------------------------

const (
	ProfessionalProfileEditTypePersonalInformation          = "PERSONAL_INFORMATION"            // 個人資料
	ProfessionalProfileEditTypeWorkPreferencesAndExperience = "WORK_PREFERENCES_AND_EXPERIENCE" // 工作偏好及經驗
	ProfessionalProfileEditTypeRegistrationAndCertification = "REGISTRATION_AND_CERTIFICATION"  // 註冊及證書
	ProfessionalProfileEditTypeProofOfIdentityAndRecords    = "PROOF_OF_IDENTITY_AND_RECORDS"   // 身份證明及記錄
	ProfessionalProfileEditTypeAdditionalCertification      = "ADDITIONAL_CERTIFICATION"        // 附加證明
)

type ProfessionalProfileEditReq struct {
	UserId                       uint64                                                  `json:"-"`
	EditType                     string                                                  `json:"editType" binding:"required,oneof=PERSONAL_INFORMATION WORK_PREFERENCES_AND_EXPERIENCE REGISTRATION_AND_CERTIFICATION PROOF_OF_IDENTITY_AND_RECORDS ADDITIONAL_CERTIFICATION"` // 編輯類型 PERSONAL_INFORMATION=個人資料、WORK_PREFERENCES_AND_EXPERIENCE=工作偏好及經驗、REGISTRATION_AND_CERTIFICATION=註冊及證書、PROOF_OF_IDENTITY_AND_RECORDS=身份證明及記錄、ADDITIONAL_CERTIFICATION=附加證明
	ProfessionalId               uint64                                                  `json:"professionalId" binding:"required"`                                                                                                                                            // 專業人士ID
	PersonalInformation          *ProfessionalProfileEditPersonalInformationReq          `json:"personalInformation" binding:"omitempty"`                                                                                                                                      // 個人資料
	WorkPreferencesAndExperience *ProfessionalProfileEditWorkPreferencesAndExperienceReq `json:"workPreferencesAndExperience" binding:"omitempty"`                                                                                                                             // 工作偏好及經驗
	RegistrationAndCertification *ProfessionalProfileEditRegistrationAndCertificationReq `json:"registrationAndCertification" binding:"omitempty"`                                                                                                                             // 註冊及證書
	ProofOfIdentityAndRecords    *ProfessionalProfileEditProofOfIdentityAndRecordsReq    `json:"proofOfIdentityAndRecords" binding:"omitempty"`                                                                                                                                // 身份證明及記錄
	AdditionalInformation        *ProfessionalProfileEditAdditionalCertificationReq      `json:"additionalInformation" binding:"omitempty"`                                                                                                                                    // 附加證明
}

type ProfessionalProfileEditPersonalInformationReq struct {
	FirstName                    string          `json:"firstName" binding:"omitempty,max=128"`
	LastName                     string          `json:"lastName" binding:"omitempty,max=128"`
	PermissionToWork             string          `json:"permissionToWork" binding:"required,oneof=AUSTRALIAN_CITIZEN PERMANENT_RESIDENT VISA NONE"` // 工作許可 AUSTRALIAN_CITIZEN=澳洲公民、PERMANENT_RESIDENT=永久居民、VISA=簽證、NONE=無
	Profession                   string          `json:"profession" binding:"required,max=255"`                                                     // 專業
	Gender                       string          `json:"gender" binding:"omitempty,oneof=MALE FEMALE NON_BINARY PREFER_NOT_TO_SAY"`                 // 性別
	DateOfBirth                  string          `json:"dateOfBirth" binding:"omitempty,datetime=2006-01-02"`                                       // 出生日期(YYYY-MM-DD)
	Address                      string          `json:"address" binding:"omitempty,max=1024"`                                                      // 居住地址
	AddressExtra                 string          `json:"addressExtra" binding:"omitempty,max=1024"`                                                 // 居住地址補充
	LocationState                string          `json:"locationState" binding:"required_with=Address,max=128"`                                     // 州
	LocationCity                 string          `json:"locationCity" binding:"required_with=Address,max=128"`                                      // 城市
	LocationRoute                string          `json:"locationRoute" binding:"omitempty,max=128"`                                                 // 街道
	LocationLat                  decimal.Decimal `json:"locationLat" binding:"required_with=Address"`                                               // 緯度
	LocationLng                  decimal.Decimal `json:"locationLng" binding:"required_with=Address"`                                               // 經度
	PreferredState               string          `json:"preferredState" binding:"omitempty,max=128"`                                                // 首選州
	PreferredLocality            string          `json:"preferredLocality" binding:"omitempty,max=128"`                                             // 首選地區
	MinimumHourlyRate            decimal.Decimal `json:"minimumHourlyRate" binding:"omitempty"`                                                     // 最低時薪
	DistanceWithin               decimal.Decimal `json:"distanceWithin" binding:"omitempty"`                                                        // 距離範圍(KM)
	Language                     string          `json:"language" binding:"omitempty,max=1024"`                                                     // 語言能力 (逗號分隔多個語言)
	PhotoFileId                  uint64          `json:"photoFileId" binding:"omitempty"`                                                           // 照片文件ID
	EmergencyContactFirstName    string          `json:"emergencyContactFirstName" binding:"omitempty,max=128"`                                     // 緊急聯絡人名字
	EmergencyContactLastName     string          `json:"emergencyContactLastName" binding:"omitempty,max=128"`                                      // 緊急聯絡人姓氏
	EmergencyContactPhone        string          `json:"emergencyContactPhone" binding:"omitempty,max=20"`                                          // 緊急聯絡人電話
	EmergencyContactRelationship string          `json:"emergencyContactRelationship" binding:"omitempty,max=255"`                                  // 緊急聯絡人關係
}

type ProfessionalProfileEditWorkPreferencesAndExperienceReq struct {
	ExperienceLevel                  string                                     `json:"experienceLevel" binding:"omitempty,max=255"`                    // 經驗級別
	PreferredSpecialities            []model.ProfessionalPreferredSpeciality    `json:"preferredSpecialities" binding:"omitempty"`                      // 首選專業
	MedicationEndorsement            string                                     `json:"medicationEndorsement" binding:"omitempty,oneof=Y N"`            // 藥物授權 Y/N (僅Enrolled Nurse)
	Experiences                      []model.ProfessionalExperience             `json:"experiences" binding:"omitempty,dive"`                           // 工作經驗
	CvNoContactDetailsConfirmed      string                                     `json:"cvNoContactDetailsConfirmed" binding:"omitempty,oneof=Y N"`      // 確認CV無聯絡方式 Y/N
	CompletedStudiesInLastThreeYears string                                     `json:"completedStudiesInLastThreeYears" binding:"omitempty,oneof=Y N"` // 過去三年內完成學習 Y/N
	Qualification                    string                                     `json:"qualification" binding:"omitempty,max=255"`                      // 學歷
	QualificationEndDate             string                                     `json:"qualificationEndDate" binding:"omitempty,datetime=2006-01-02"`   // 學歷結束日期(YYYY-MM-DD)
	QualificationName                string                                     `json:"qualificationName" binding:"omitempty,max=255"`                  // 學歷名稱
	GraduationInstitution            string                                     `json:"graduationInstitution" binding:"omitempty,max=255"`              // 畢業院校
	InstitutionCountry               string                                     `json:"institutionCountry" binding:"omitempty,max=255"`                 // 院校國家
	GraduationYear                   int32                                      `json:"graduationYear" binding:"omitempty,min=1900,max=2200"`           // 畢業年份(yyyy)
	References                       []ProfessionalReferenceEditReq             `json:"references" binding:"omitempty,max=2,dive"`                      // 推薦人
	ReferencesTermsConfirm           string                                     `json:"referencesTermsConfirm" binding:"omitempty,oneof=Y N"`           // 推薦人協議確認
	Files                            map[string][]model.ProfessionalProfileFile `json:"files" binding:"omitempty,dive"`                                 // 文件 個人護理工作資格文件=PPCWQ_EXPERIENCE_AGED_CARE_DISABILITY	PPCWQ_CERT_III_AGED_CARE PPCWQ_CERT_III_DISABILITIES PPCWQ_CERT_III_INDIVIDUAL_SUPPORT PPCWQ_CERT_III_INDIVIDUAL_SUPPORT_AGED PPCWQ_CERT_III_INDIVIDUAL_SUPPORT_DISABILITY PPCWQ_CERT_III_HOME_COMMUNITY_CARE PPCWQ_CERT_IV_AGED_CARE PPCWQ_CERT_IV_DISABILITIES PPCWQ_CERT_IV_HOME_COMMUNITY_CARE PPCWQ_DEGREE_ALLIED_HEALTH PPCWQ_DEGREE_NURSING PPCWQ_OTHER_RELEVANT PPCWQ_WORKING_TOWARD_NURSING 學歷資格證書=QUALIFICATION_CERTIFICATE Registrar入學證明=REGISTRAR_ACCREDITED_ENROLMENT Fellowship證書=FELLOWSHIP_CERTIFICATE Specialist資格證明=SPECIALIST_QUALIFICATION
}

type ProfessionalReferenceEditReq struct {
	ReferenceFirstName      string `json:"referenceFirstName" binding:"omitempty,max=128"`                  // 推薦人名字
	ReferenceLastName       string `json:"referenceLastName" binding:"omitempty,max=128"`                   // 推薦人姓氏
	ReferenceEmail          string `json:"referenceEmail" binding:"omitempty,email"`                        // 推薦人電郵
	ReferenceFacility       string `json:"referenceFacility" binding:"omitempty,max=255"`                   // 推薦人機構
	ReferenceRole           string `json:"referenceRole" binding:"omitempty,max=255"`                       // 推薦人職位
	DateWorkedTogetherBegin string `json:"dateWorkedTogetherBegin" binding:"omitempty,datetime=2006-01-02"` // 一起工作的開始時間
	DateWorkedTogetherEnd   string `json:"dateWorkedTogetherEnd" binding:"omitempty,datetime=2006-01-02"`   // 一起工作的結束時間，如果是空，說明是現在還在持續
}

type ProfessionalProfileEditRegistrationAndCertificationReq struct {
	SoleTrader string                                     `json:"soleTrader" binding:"omitempty,oneof=Y N"` // 是否為個人獨資 Y/N
	AbnInfo    AbnInfo                                    `json:"-"`                                        // ABN信息
	AbnResp    *AbnDetailQueryResp                        `json:"-"`                                        // ABN詳細信息
	Files      map[string][]model.ProfessionalProfileFile `json:"files" binding:"omitempty,dive"`           // 文件 AHPRA證書=AHPRA_CERT PROFESSIONAL_ABN=ABN 專業人士責任保險證明=IND_INSURANCE_CERT
}

type ProfessionalProfileEditProofOfIdentityAndRecordsReq struct {
	HasOverseasCitizenshipOrPr                  string                                     `json:"hasOverseasCitizenshipOrPr" binding:"omitempty,oneof=Y N"`                // 是否擁有海外公民身份或永久居留權 Y/N
	IdCheckDeletionConsent                      string                                     `json:"idCheckDeletionConsent" binding:"required,oneof=Y N"`                     // 用戶是否同意ID文件刪除聲明 Y/N
	IdCheckFileTypes                            string                                     `json:"idCheckFileTypes"`                                                        // 已審核的ID文件類型（逗號分隔）
	WorkingWithChildrenOrVulnerablePeopleStates string                                     `json:"workingWithChildrenOrVulnerablePeopleSelectedStates" binding:"omitempty"` // 有兒童/脆弱人群工作資格的地區（必填）
	Files                                       map[string][]model.ProfessionalProfileFile `json:"files" binding:"omitempty,dive"`                                          // 文件 PROFESSIONAL_ID_CHECK=專業人士身份證明文件（Primary:AUSTRALIAN_PASSPORT=澳洲護照,FOREIGN_PASSPORT=外國護照,AUSTRALIAN_BIRTH_CERT=澳洲出生證明,AUSTRALIAN_CITIZENSHIP_CERT=澳洲公民證 Secondary:CURRENT_AUSTRALIA_DRIVER_LICENCE=澳洲駕照,AUSTRALIAN_PUBLIC_SERVICE_EMPLOYEE_ID_CARD=澳洲公務員ID卡,OTHER_AUSTRALIAN_GOVERNMENT_ISSUE_ID_CARD=其他澳洲政府發出的ID卡,TERTIARY_STUDENT_ID_CARD=大學生ID卡 Others:CREDIT_DEBIT_ATM_CARD=信用卡/扣帳卡/ATM卡,MEDICARE_CARD=醫療卡,UTILITY_BILL_OR_RATE_NOTICE=水電費單或收費通知,STATEMENT_FROM_FINANCIAL_INSTITUTION=金融機構的結單,CENTRELINK_OR_PENSION_CARD=澳洲国民福利署或養老金卡）,VISA=簽證,NATIONAL_CRIMINAL_CHECK=國家犯罪檢查,WORKING_WITH_CHILDREN_OR_VULNERABLE_PEOPLE=兒童/脆弱人群工作檢查,CURRENT_IMMUNISATION_RECORDS=現在的免疫記錄,COMMONWEALTH_STATUTORY_DECLARATION=聯邦法定聲明
}

type ProfessionalProfileEditAdditionalCertificationReq struct {
	HasCompletedInfectionControlTraining string                                     `json:"hasCompletedInfectionControlTraining" binding:"omitempty,oneof=Y N"` // 是否完成感染控制培訓 Y/N
	DisclosureQuestions                  []model.ProfessionalDisclosureQuestion     `json:"disclosureQuestions" binding:"omitempty,dive"`                       // 披露問題
	Files                                map[string][]model.ProfessionalProfileFile `json:"files" binding:"omitempty,dive"`                                     // 文件 附加證明
}

func (s *professionalProfileService) ProfessionalProfileEdit(db *gorm.DB, req ProfessionalProfileEditReq) error {
	var err error
	professional, profile, err := s.GetProfessionalProfile(db, req.ProfessionalId)
	if err != nil {
		return err
	}
	// Personal Information
	// 個人資料
	if req.PersonalInformation != nil {
		if err = s.UpdatePersonalInformation(db, &professional, &profile, req); err != nil {
			return err
		}
	}

	// Work Preferences & Experience
	// 工作偏好及經驗
	if req.WorkPreferencesAndExperience != nil {
		if err = s.UpdateWorkPreferencesAndExperience(db, &professional, &profile, req); err != nil {
			return err
		}
	}

	// Registration & Certification
	// 註冊及證書
	if req.RegistrationAndCertification != nil {
		if err = s.UpdateRegistrationAndCertification(db, &professional, &profile, req); err != nil {
			return err
		}
	}

	// Proof of Identity & Records
	// 身份證明及記錄
	if req.ProofOfIdentityAndRecords != nil {
		if err = s.UpdateProofOfIdentityAndRecords(db, &professional, &profile, req); err != nil {
			return err
		}
	}

	// Additional Certification
	// 附加證明
	if req.AdditionalInformation != nil {
		if err = s.UpdateAdditionalCertification(db, &profile, req); err != nil {
			return err
		}
	}
	professional.IdCheckExpiryDate, err = s.GetFilesEarliestExpiryDate(profile.Files)
	if err != nil {
		return err
	}

	// 保存
	if err = professional.MarshalProfile(profile); err != nil {
		return err
	}
	if err = db.Save(&professional).Error; err != nil {
		return err
	}
	return nil
}

// 更新個人資料
func (s *professionalProfileService) UpdatePersonalInformation(db *gorm.DB, professional *model.Professional, profile *model.ProfessionalProfile, req ProfessionalProfileEditReq) error {
	// 更換職業
	if professional.Profession != req.PersonalInformation.Profession {
		// 專業改變，則清空專業細分
		professional.ExperienceLevel = ""
		profile.Experiences = []model.ProfessionalExperience{}
		profile.CompletedStudiesInLastThreeYears = ""
		profile.Qualification = ""
		profile.QualificationEndDate = ""
		profile.PreferredSpecialities = make([]model.ProfessionalPreferredSpeciality, 0)
		// 清空個人護理工作資格文件
		var cleanFileCodes []string
		cleanFileCodes, err := SelectionService.GetCodeByType(db, model.ProfessionalFileCodePersonalCareWorkQualification)
		if err != nil {
			return err
		}
		// 清空學歷資格證書文件
		cleanFileCodes = append(cleanFileCodes, model.ProfessionalFileCodeCurriculumVitae)
		if req.PersonalInformation.Profession == model.ProfessionalProfessionPersonalCareWorker {
			// 清空學歷資格證書文件（當從其他職業切換到個人護理工作時）
			cleanFileCodes = append(cleanFileCodes, model.ProfessionalFileCodeQualificationCertificate)
		}
		emptyFile := map[string][]model.ProfessionalProfileFile{}
		for _, code := range cleanFileCodes {
			if err = s.updateSingleFileType(db, req, profile, emptyFile, code); err != nil {
				return err
			}
		}
	}

	professional.FirstName = req.PersonalInformation.FirstName
	professional.LastName = req.PersonalInformation.LastName

	// 處理Permission to Work變更時的文件清理
	if professional.PermissionToWork != req.PersonalInformation.PermissionToWork {
		disabledFileTypes := s.GetDisabledFileTypesByPermissionToWork(req.PersonalInformation.PermissionToWork)
		if len(disabledFileTypes) > 0 {
			emptyFile := map[string][]model.ProfessionalProfileFile{}
			for _, fileType := range disabledFileTypes {
				if err := s.updateSingleFileType(db, req, profile, emptyFile, fileType); err != nil {
					return err
				}
			}
		}
	}

	professional.PermissionToWork = req.PersonalInformation.PermissionToWork
	professional.Profession = req.PersonalInformation.Profession
	professional.Language = req.PersonalInformation.Language
	professional.Gender = req.PersonalInformation.Gender
	if req.PersonalInformation.DateOfBirth != "" {
		professional.DateOfBirth = xtype.NewNullDate(req.PersonalInformation.DateOfBirth)
	} else {
		professional.DateOfBirth = xtype.NewNullDate()
	}
	professional.Address = req.PersonalInformation.Address
	professional.AddressExtra = req.PersonalInformation.AddressExtra
	professional.LocationLat = req.PersonalInformation.LocationLat
	professional.LocationLng = req.PersonalInformation.LocationLng
	professional.LocationState = req.PersonalInformation.LocationState
	professional.LocationCity = req.PersonalInformation.LocationCity
	professional.LocationRoute = req.PersonalInformation.LocationRoute
	professional.DistanceWithin = req.PersonalInformation.DistanceWithin
	professional.PreferredState = req.PersonalInformation.PreferredState
	professional.PreferredLocality = req.PersonalInformation.PreferredLocality
	professional.MinimumHourlyRate = req.PersonalInformation.MinimumHourlyRate
	profile.EmergencyContactFirstName = req.PersonalInformation.EmergencyContactFirstName
	profile.EmergencyContactLastName = req.PersonalInformation.EmergencyContactLastName
	profile.EmergencyContactPhone = req.PersonalInformation.EmergencyContactPhone
	profile.EmergencyContactRelationship = req.PersonalInformation.EmergencyContactRelationship

	// 照片文件
	ProfessionalFileIds := make([]uint64, 0)
	if req.PersonalInformation.PhotoFileId != 0 {
		ProfessionalFileIds = append(ProfessionalFileIds, req.PersonalInformation.PhotoFileId)
	}
	photoFile := model.ProfessionalProfileFile{
		ProfessionalFileIds: ProfessionalFileIds,
		FileCode:            model.ProfessionalFileCodePhoto,
	}
	if err := s.UpdateProfileFiles(db, req, profile, []model.ProfessionalProfileFile{photoFile}, []string{model.ProfessionalFileCodePhoto}); err != nil {
		return err
	}
	return nil
}

// 更新工作偏好及經驗
func (s *professionalProfileService) UpdateWorkPreferencesAndExperience(db *gorm.DB, professional *model.Professional, profile *model.ProfessionalProfile, req ProfessionalProfileEditReq) error {
	var err error
	professional.ExperienceLevel = req.WorkPreferencesAndExperience.ExperienceLevel
	professional.MedicationEndorsement = req.WorkPreferencesAndExperience.MedicationEndorsement
	profile.PreferredSpecialities = req.WorkPreferencesAndExperience.PreferredSpecialities
	profile.CvNoContactDetailsConfirmed = req.WorkPreferencesAndExperience.CvNoContactDetailsConfirmed
	profile.CompletedStudiesInLastThreeYears = req.WorkPreferencesAndExperience.CompletedStudiesInLastThreeYears
	profile.Qualification = req.WorkPreferencesAndExperience.Qualification
	profile.QualificationEndDate = req.WorkPreferencesAndExperience.QualificationEndDate
	profile.ReferencesTermsConfirm = req.WorkPreferencesAndExperience.ReferencesTermsConfirm

	// 畢業院校信息
	profile.QualificationName = req.WorkPreferencesAndExperience.QualificationName
	profile.GraduationInstitution = req.WorkPreferencesAndExperience.GraduationInstitution
	profile.InstitutionCountry = req.WorkPreferencesAndExperience.InstitutionCountry
	professional.GraduationYear = req.WorkPreferencesAndExperience.GraduationYear

	// 處理學歷資格證書文件 - 只有Medical Practitioner、Registered Nurse、Enrolled Nurse需要
	if professional.Profession == model.ProfessionalProfessionMedicalPractitioner ||
		professional.Profession == model.ProfessionalProfessionRegisteredNurse ||
		professional.Profession == model.ProfessionalProfessionEnrolledNurse {
		if err = s.updateSingleFileType(db, req, profile, req.WorkPreferencesAndExperience.Files, model.ProfessionalFileCodeQualificationCertificate); err != nil {
			return err
		}
	}

	// 處理Medical Practitioner的Preferred Grade相關文件
	if professional.Profession == model.ProfessionalProfessionMedicalPractitioner {
		hasRegistrarAccreditedEnrolment := false
		hasFellowshipCertificate := false
		hasSpecialistQualification := false
		for _, preferredSpeciality := range profile.PreferredSpecialities {
			if preferredSpeciality.Grade == model.ProfessionalPreferredGradeRegistrarAccredited {
				hasRegistrarAccreditedEnrolment = true
			}
			if preferredSpeciality.Grade == model.ProfessionalPreferredGradeFellow {
				hasFellowshipCertificate = true
			}
			if preferredSpeciality.Grade == model.ProfessionalPreferredGradeSpecialist {
				hasSpecialistQualification = true
			}
		}
		if hasRegistrarAccreditedEnrolment {
			if err = s.updateSingleFileType(db, req, profile, req.WorkPreferencesAndExperience.Files, model.ProfessionalFileCodeRegistrarAccreditedEnrolment); err != nil {
				return err
			}
		}
		if hasFellowshipCertificate {
			if err = s.updateSingleFileType(db, req, profile, req.WorkPreferencesAndExperience.Files, model.ProfessionalFileCodeFellowshipCertificate); err != nil {
				return err
			}
		}
		if hasSpecialistQualification {
			if err = s.updateSingleFileType(db, req, profile, req.WorkPreferencesAndExperience.Files, model.ProfessionalFileCodeSpecialistQualification); err != nil {
				return err
			}
		}
	}

	var personalCareWorkerQualificationCode []string
	personalCareWorkerQualificationCode, err = SelectionService.GetCodeByType(db, model.ProfessionalFileCodePersonalCareWorkQualification)
	if err != nil {
		return err
	}
	for _, code := range personalCareWorkerQualificationCode {
		// 個人護理工作資格文件
		if _, exists := req.WorkPreferencesAndExperience.Files[code]; exists {
			for fileCode, files := range req.WorkPreferencesAndExperience.Files {
				if len(files) == 0 {
					continue
				}
				if err = s.UpdateProfileFiles(db, req, profile, files, []string{fileCode}); err != nil {
					return err
				}
			}
		} else {
			// 如果不存在，則刪除
			emptyFile := map[string][]model.ProfessionalProfileFile{}
			if err = s.updateSingleFileType(db, req, profile, emptyFile, code); err != nil {
				return err
			}
		}
	}

	// 工作經驗
	if len(req.WorkPreferencesAndExperience.Experiences) > 0 {
		profile.Experiences = req.WorkPreferencesAndExperience.Experiences
	} else {
		profile.Experiences = []model.ProfessionalExperience{}
	}
	// 保存Curriculum Vitae文件
	if err = s.updateSingleFileType(db, req, profile, req.WorkPreferencesAndExperience.Files, model.ProfessionalFileCodeCurriculumVitae); err != nil {
		return err
	}

	// 对比推薦人
	if len(req.WorkPreferencesAndExperience.References) > 0 {
		for i, ref := range req.WorkPreferencesAndExperience.References {
			if i < len(profile.References) {
				currentReference := profile.References[i]
				var currentProfessionalReferenceEditReq ProfessionalReferenceEditReq
				_ = copier.Copy(&currentProfessionalReferenceEditReq, currentReference)
				if !cmp.Equal(currentProfessionalReferenceEditReq, ref) {
					// 重置 form
					var reference model.ProfessionalReference // 新 form
					_ = copier.Copy(&reference, ref)
					profile.References[i] = reference
				}
			} else {
				var reference model.ProfessionalReference // 新 form
				_ = copier.Copy(&reference, ref)
				profile.References = append(profile.References, reference)
			}
		}
	}

	// 重置 ReferenceFormStatus
	professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusFilled
	for _, reference := range profile.References {
		if reference.FormStatus == model.ProfessionalProfileReferencesFormStatusPending || reference.FormStatus == "" {
			professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusUnfilled
			break
		}
	}

	return nil
}

func (s *professionalProfileService) getPreferredGrade(profile *model.ProfessionalProfile) map[string]bool {
	var gradeMap = make(map[string]bool)
	for _, preferredSpeciality := range profile.PreferredSpecialities {
		gradeMap[preferredSpeciality.Grade] = true
	}
	return gradeMap
}
func (s *professionalProfileService) getPreferredSpecialitiesValid(profession string, profile *model.ProfessionalProfile) bool {
	needGrade := false
	if profession == model.ProfessionalProfessionMedicalPractitioner {
		needGrade = true
	}
	if len(profile.PreferredSpecialities) == 0 {
		return false
	}
	for _, preferredSpeciality := range profile.PreferredSpecialities {
		if preferredSpeciality.Speciality == "" {
			return false
		}
		if needGrade && preferredSpeciality.Grade == "" {
			return false
		}
	}
	return true
}

// 更新單個文件類型
func (s *professionalProfileService) updateSingleFileType(db *gorm.DB, req ProfessionalProfileEditReq, profile *model.ProfessionalProfile, files map[string][]model.ProfessionalProfileFile, fileCode string) error {
	fileList := make([]model.ProfessionalProfileFile, 0)
	if fileTypeFiles, exists := files[fileCode]; exists && fileTypeFiles != nil {
		fileList = fileTypeFiles
	}
	return s.UpdateProfileFiles(db, req, profile, fileList, []string{fileCode})
}

// 更新文件和數據庫中的文件關係
func (s *professionalProfileService) UpdateProfileFiles(db *gorm.DB, req ProfessionalProfileEditReq, profile *model.ProfessionalProfile, files []model.ProfessionalProfileFile, fileCodes []string) error {
	// 創建文件代碼映射
	codeMap := make(map[string]bool)
	for _, code := range fileCodes {
		codeMap[code] = true
	}
	const keyFormat = "%s||%s"

	// 創建現有文件映射
	existingFileMap := make(map[string][]model.ProfessionalProfileFile)
	for _, file := range profile.Files {
		var idsStrArr []string
		for _, id := range file.ProfessionalFileIds {
			idsStrArr = append(idsStrArr, fmt.Sprintf("%d", id))
		}
		key := fmt.Sprintf(keyFormat, file.FileCode, strings.Join(idsStrArr, "_")) // 文件編號_文件ID
		existingFileMap[key] = append(existingFileMap[key], file)
	}

	// 創建新的文件列表
	var newFiles []model.ProfessionalProfileFile
	newProfessionalFileRelations := make([]model.ProfessionalFileRelation, 0)
	keepFileIdMap := make(map[string]bool)
	needDeleteProfessionalFileRelations := make([]model.ProfessionalFileRelation, 0)

	// 處理更新和新增
	for _, file := range files {
		if _, exists := codeMap[file.FileCode]; exists {
			var idsStrArr []string
			for _, id := range file.ProfessionalFileIds {
				idsStrArr = append(idsStrArr, fmt.Sprintf("%d", id))
			}
			key := fmt.Sprintf(keyFormat, file.FileCode, strings.Join(idsStrArr, "_")) // 文件編號_文件ID
			// 如果文件代碼存在於 codeMap 中
			if existingFile, exists := existingFileMap[key]; exists {
				// 現有文件ID沒有改動
				// 更新文件到期日、描述和編號
				for i := range existingFile {
					existingFile[i].ExpiryDate = file.ExpiryDate
					existingFile[i].Description = file.Description
					existingFile[i].Number = file.Number
				}

				newFiles = append(newFiles, existingFile...)
				keepFileIdMap[key] = true
			} else {
				// 新增文件
				newFiles = append(newFiles, file)
				for _, id := range file.ProfessionalFileIds {
					newProfessionalFileRelations = append(newProfessionalFileRelations, model.ProfessionalFileRelation{
						UserId:             req.UserId,
						ProfessionalId:     req.ProfessionalId,
						ProfessionalFileId: id,
					})
				}
			}
		} else {
			// 如果文件代碼不存在於 codeMap 中，則保留該文件
			newFiles = append(newFiles, file)
		}
	}

	// 處理刪除
	for _, existingFile := range profile.Files {
		if _, exists := codeMap[existingFile.FileCode]; !exists {
			// 如果文件編號不在 codeMap 中，則保留該文件
			newFiles = append(newFiles, existingFile)
			continue
		}

		var idsStrArr []string
		for _, id := range existingFile.ProfessionalFileIds {
			idsStrArr = append(idsStrArr, fmt.Sprintf("%d", id))
		}
		key := fmt.Sprintf(keyFormat, existingFile.FileCode, strings.Join(idsStrArr, "_")) // 文件編號_文件ID
		if _, exists := keepFileIdMap[key]; !exists {
			for _, id := range existingFile.ProfessionalFileIds {
				needDeleteProfessionalFileRelations = append(needDeleteProfessionalFileRelations, model.ProfessionalFileRelation{
					ProfessionalId:     req.ProfessionalId,
					ProfessionalFileId: id,
				})
			}
		}

		// 刪除文件關係
		for _, needDeleteProfessionalFileRelation := range needDeleteProfessionalFileRelations {
			if err := db.Where("professional_id = ?", needDeleteProfessionalFileRelation.ProfessionalId).
				Where("professional_file_id = ?", needDeleteProfessionalFileRelation.ProfessionalFileId).
				Where("professional_id = ?", req.ProfessionalId).
				Delete(&model.ProfessionalFileRelation{}).Error; err != nil {
				return err
			}
		}
	}

	// 創建新的文件關係
	if len(newProfessionalFileRelations) > 0 {
		if err := db.Create(&newProfessionalFileRelations).Error; err != nil {
			return err
		}
	}

	// 更新 profile.Files
	profile.Files = newFiles
	return nil
}

// 更新註冊及證書
func (s *professionalProfileService) UpdateRegistrationAndCertification(db *gorm.DB, professional *model.Professional, profile *model.ProfessionalProfile, req ProfessionalProfileEditReq) error {
	var err error

	// 定義需要處理的文件類型
	fileTypes := []string{
		model.ProfessionalFileCodeAhpraCertificate,              // AHPRA文件
		model.ProfessionalFileCodeAbn,                           // ABN文件
		model.ProfessionalFileCodeIndemnityInsuranceCertificate, // 專業人士責任保險證明文件
	}

	// 批量處理文件
	for _, fileCode := range fileTypes {
		if err = s.updateSingleFileType(db, req, profile, req.RegistrationAndCertification.Files, fileCode); err != nil {
			return err
		}
	}

	// 提取ABN
	abnFiles := req.RegistrationAndCertification.Files[model.ProfessionalFileCodeAbn]
	var abnNumber string
	if len(abnFiles) > 0 {
		abnFile := abnFiles[0]
		if abnFile.Number != "" {
			abnNumber = abnFile.Number
		}
	}
	profile.SoleTrader = req.RegistrationAndCertification.SoleTrader
	// 更新ABN, ABN有變化AbnInfo才有值
	if abnNumber != professional.AbnNumber {
		professional.AbnNumber = abnNumber
		professional.AbnEntityName = req.RegistrationAndCertification.AbnInfo.EntityName
		professional.AbnEntityType = req.RegistrationAndCertification.AbnInfo.EntityType
		professional.AbnValid = "N"
		if req.RegistrationAndCertification.AbnInfo.AbnNumber != "" {
			professional.AbnValid = "Y"
		}
	}
	// 更新ABN的GST
	if req.RegistrationAndCertification.AbnResp != nil {
		if err = AbnService.UpdateProfessionalGstFromAbn(db, req.UserId, req.ProfessionalId, *req.RegistrationAndCertification.AbnResp); err != nil {
			return err
		}
	}
	// 提取Ahpra
	ahpraFiles := req.RegistrationAndCertification.Files[model.ProfessionalFileCodeAhpraCertificate]
	professional.AhpraNumber = ""
	professional.AhpraExpiryDate = xtype.NewNullDate()
	if len(ahpraFiles) > 0 {
		ahpraFile := ahpraFiles[0]
		if ahpraFile.Number != "" {
			professional.AhpraNumber = ahpraFile.Number
		}
		if ahpraFile.ExpiryDate != "" {
			professional.AhpraExpiryDate = xtype.NewNullDate(ahpraFile.ExpiryDate)
		}
	}

	return nil
}

var idCheckFileCodeMap = map[string]string{
	model.ProfessionalFileCodeAustralianPassport:                    "PRIMARY",
	model.ProfessionalFileCodeForeignPassport:                       "PRIMARY",
	model.ProfessionalFileCodeAustralianBirthCertificate:            "PRIMARY",
	model.ProfessionalFileCodeAustralianCitizenshipCertificate:      "PRIMARY",
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:         "SECONDARY",
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: "SECONDARY",
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  "SECONDARY",
	model.ProfessionalFileCodeTertiaryStudentIDCard:                 "SECONDARY",
	model.ProfessionalFileCodeCreditDebitAtmCard:                    "OTHERS",
	model.ProfessionalFileCodeMedicareCard:                          "OTHERS",
	model.ProfessionalFileCodeUtilityBillOrRateNotice:               "OTHERS",
	model.ProfessionalFileCodeStatementFromFinancialInstitution:     "OTHERS",
	model.ProfessionalFileCodeCentrelinkOrPensionCard:               "OTHERS",
}

// 更新證明文件
func (s *professionalProfileService) UpdateProofOfIdentityAndRecords(db *gorm.DB, professional *model.Professional, profile *model.ProfessionalProfile, req ProfessionalProfileEditReq) error {
	var err error

	// 根據Permission to Work清理不相關的文件
	disabledFileTypes := s.GetDisabledFileTypesByPermissionToWork(professional.PermissionToWork)
	if len(disabledFileTypes) > 0 {
		emptyFile := map[string][]model.ProfessionalProfileFile{}
		for _, fileType := range disabledFileTypes {
			// 檢查是否是身份證明相關的文件類型
			if fileType == model.ProfessionalFileCodeAustralianPassport ||
				fileType == model.ProfessionalFileCodeForeignPassport ||
				fileType == model.ProfessionalFileCodeAustralianCitizenshipCertificate {
				if err = s.updateSingleFileType(db, req, profile, emptyFile, fileType); err != nil {
					return err
				}
			}
		}
	}

	// 身份證明文件
	idCheckFiles := make([]model.ProfessionalProfileFile, 0)
	if files, exists := req.ProofOfIdentityAndRecords.Files[model.ProfessionalFileCodeIdCheck]; exists && files != nil {
		idCheckFiles = files
	}
	for _, idCheckFile := range idCheckFiles {
		if _, exists := idCheckFileCodeMap[idCheckFile.FileCode]; !exists {
			continue
		}
		if err = s.UpdateProfileFiles(db, req, profile, []model.ProfessionalProfileFile{idCheckFile}, []string{idCheckFile.FileCode}); err != nil {
			return err
		}
	}

	// 定義需要處理的文件類型
	fileTypes := []string{
		model.ProfessionalFileCodeAustralianPassport,
		model.ProfessionalFileCodeForeignPassport,
		model.ProfessionalFileCodeAustralianBirthCertificate,
		model.ProfessionalFileCodeAustralianCitizenshipCertificate,
		model.ProfessionalFileCodeCurrentAustraliaDriverLicence,
		model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard,
		model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard,
		model.ProfessionalFileCodeTertiaryStudentIDCard,
		model.ProfessionalFileCodeCreditDebitAtmCard,
		model.ProfessionalFileCodeMedicareCard,
		model.ProfessionalFileCodeUtilityBillOrRateNotice,
		model.ProfessionalFileCodeStatementFromFinancialInstitution,
		model.ProfessionalFileCodeCentrelinkOrPensionCard,

		model.ProfessionalFileCodeVisa,                                  // 簽證
		model.ProfessionalFileCodeNationalCriminalCheck,                 // 國家犯罪檢查
		model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople, // 兒童/脆弱人群工作檢查
		model.ProfessionalFileCodeCurrentImmunisationRecords,            // 現在的免疫記錄
	}

	// 批量處理文件
	for _, fileCode := range fileTypes {
		if err = s.updateSingleFileType(db, req, profile, req.ProofOfIdentityAndRecords.Files, fileCode); err != nil {
			return err
		}
	}

	// 是否擁有海外公民身份或永久居留權
	profile.HasOverseasCitizenshipOrPr = req.ProofOfIdentityAndRecords.HasOverseasCitizenshipOrPr

	// ID文件刪除同意
	profile.IdCheckDeletionConsent = req.ProofOfIdentityAndRecords.IdCheckDeletionConsent

	// 已審核的ID文件類型
	profile.IdCheckFileTypes = req.ProofOfIdentityAndRecords.IdCheckFileTypes

	// 兒童/脆弱人群工作資格地區
	profile.WorkingWithChildrenOrVulnerablePeopleStates = req.ProofOfIdentityAndRecords.WorkingWithChildrenOrVulnerablePeopleStates

	// 聯邦法定聲明
	if profile.HasOverseasCitizenshipOrPr == "Y" {
		if err = s.updateSingleFileType(db, req, profile, req.ProofOfIdentityAndRecords.Files, model.ProfessionalFileCodeCommonwealthStatutoryDeclaration); err != nil {
			return err
		}
	}

	return nil
}

// 更新附加證明
func (s *professionalProfileService) UpdateAdditionalCertification(db *gorm.DB, profile *model.ProfessionalProfile, req ProfessionalProfileEditReq) error {
	var err error
	profile.HasCompletedInfectionControlTraining = req.AdditionalInformation.HasCompletedInfectionControlTraining
	profile.DisclosureQuestions = req.AdditionalInformation.DisclosureQuestions

	fileTypes := []string{
		model.ProfessionalFileCodeAdditionalCertification, // 附加證明
		model.ProfessionalFileCodeDisclosure,              // 披露文件
		model.ProfessionalFileCodeSignedAgreement,         // 簽署協議
	}
	// 批量處理文件
	for _, fileCode := range fileTypes {
		if err = s.updateSingleFileType(db, req, profile, req.AdditionalInformation.Files, fileCode); err != nil {
			return err
		}
	}

	return nil
}

// 獲取文件最早的到期日
func (s *professionalProfileService) GetFilesEarliestExpiryDate(files []model.ProfessionalProfileFile) (xtype.NullDate, error) {
	var earliestExpiryDate time.Time
	for _, file := range files {
		if file.ExpiryDate == "" {
			continue
		}
		fileExpiryDate, err := time.Parse(xtool.DateDayA, file.ExpiryDate)
		if err != nil {
			return xtype.NewNullDate(), err
		}
		if earliestExpiryDate.IsZero() || fileExpiryDate.Before(earliestExpiryDate) {
			earliestExpiryDate = fileExpiryDate
		}
	}
	if earliestExpiryDate.IsZero() {
		return xtype.NewNullDate(), nil
	}
	return xtype.NewNullDate(earliestExpiryDate.Format(xtool.DateDayA)), nil
}

// endregion ---------------------------------------------------- 編輯 ----------------------------------------------------

//region ---------------------------------------------------- 獲取專業人士資料進度 ----------------------------------------------------

type ProfessionalProfileProgressReq struct {
	ProfessionalId uint64 `json:"professionalId"`
}

type ProfessionalProfileProgress struct {
	ProfessionalId uint64 `json:"professionalId"`
	ProfessionalProfilePartProgress
	PersonalInformation          ProfessionalProfilePartProgress `json:"personalInformation"`          // 個人資料
	WorkPreferencesAndExperience ProfessionalProfilePartProgress `json:"workPreferencesAndExperience"` // 工作偏好及經驗
	RegistrationAndCertification ProfessionalProfilePartProgress `json:"registrationAndCertification"` // 註冊及證書
	ProofOfIdentityAndRecords    ProfessionalProfilePartProgress `json:"proofOfIdentityAndRecords"`    // 身份證明文件及記錄
	AdditionalInformation        ProfessionalProfilePartProgress `json:"additionalInformation"`        // 附加資料
}

type ProfessionalProfilePartProgress struct {
	Total    int  `json:"total"`    // 總分
	Current  int  `json:"current"`  // 當前分數
	Progress int  `json:"progress"` // 進度 0-100
	HasEmpty bool `json:"hasEmpty"` // 是否有空值(不計入分數的)
}

func (p *ProfessionalProfilePartProgress) AddPoints(value interface{}) {
	p.Total++
	switch v := value.(type) {
	case bool:
		if v {
			p.Current++
		}
	case string:
		if v != "" {
			p.Current++
		}
	case decimal.Decimal:
		if !v.IsZero() {
			p.Current++
		}
	case xtype.JsonNullString:
		if v.Valid {
			p.Current++
		}
	case xtype.NullDate:
		if v.Valid {
			p.Current++
		}
	case xtype.Date:
		if v.String() != "" {
			p.Current++
		}
	}
	p.Progress = p.Current * 100 / p.Total
}

func (p *ProfessionalProfilePartProgress) AddFilePoints(files []model.ProfessionalProfileFile, fileCode string, minFileQty int) {
	if minFileQty > 0 {
		p.Total++
		// 計算文件數量
		qty := 0
		for _, file := range files {
			if file.FileCode == fileCode {
				qty = qty + len(file.ProfessionalFileIds)
			}
		}
		if qty >= minFileQty {
			p.Current++
		}
	}

	// 計算文件參數
	find := false
	needParam := model.ProfessionalProfileFileNeedParamMap[fileCode]
	for _, file := range files {
		if file.FileCode != fileCode {
			continue
		}
		find = true
		if needParam&model.ProfessionalProfileFileNeedExpiryDate != 0 {
			p.AddPoints(file.ExpiryDate)
		}
		if needParam&model.ProfessionalProfileFileNeedNumber != 0 {
			p.AddPoints(file.Number)
		}
		if needParam&model.ProfessionalProfileFileNeedDescription != 0 {
			p.AddPoints(file.Description)
		}
	}
	if !find && minFileQty > 0 {
		if needParam&model.ProfessionalProfileFileNeedExpiryDate != 0 {
			p.AddPoints(false)
		}
		if needParam&model.ProfessionalProfileFileNeedNumber != 0 {
			p.AddPoints(false)
		}
		if needParam&model.ProfessionalProfileFileNeedDescription != 0 {
			p.AddPoints(false)
		}
	}

	p.Progress = p.Current * 100 / p.Total
}

func (s *professionalProfileService) ProfessionalProfileProgress(db *gorm.DB, req ProfessionalProfileProgressReq) (ProfessionalProfileProgress, error) {
	var err error
	resp := ProfessionalProfileProgress{
		ProfessionalId: req.ProfessionalId,
	}
	professional, profile, err := s.GetProfessionalProfile(db, req.ProfessionalId)
	if err != nil {
		return resp, err
	}
	// 個人資料
	resp.PersonalInformation.AddPoints(professional.FirstName)
	resp.PersonalInformation.AddPoints(professional.LastName)
	resp.PersonalInformation.AddPoints(professional.PermissionToWork)
	resp.PersonalInformation.AddPoints(professional.Profession)
	resp.PersonalInformation.AddPoints(professional.Gender)
	resp.PersonalInformation.AddPoints(professional.DateOfBirth)
	resp.PersonalInformation.AddPoints(professional.Address)
	resp.PersonalInformation.AddPoints(profile.EmergencyContactFirstName)
	resp.PersonalInformation.AddPoints(profile.EmergencyContactLastName)
	resp.PersonalInformation.AddPoints(profile.EmergencyContactPhone)
	resp.PersonalInformation.AddPoints(profile.EmergencyContactRelationship)

	resp.PersonalInformation.AddFilePoints(profile.Files, model.ProfessionalFileCodePhoto, 1)

	// 工作偏好及經驗
	areasOfExperienceValid := s.getPreferredSpecialitiesValid(professional.Profession, &profile)
	resp.WorkPreferencesAndExperience.AddPoints(areasOfExperienceValid)

	switch professional.Profession {
	case model.ProfessionalProfessionMedicalPractitioner:
		preferredGradeMap := s.getPreferredGrade(&profile)
		// 根據Preferred Grade添加相應的文件要求
		for grade := range preferredGradeMap {
			switch grade {
			case model.ProfessionalPreferredGradeRegistrarAccredited:
				resp.WorkPreferencesAndExperience.AddFilePoints(profile.Files, model.ProfessionalFileCodeRegistrarAccreditedEnrolment, 1)
			case model.ProfessionalPreferredGradeFellow:
				resp.WorkPreferencesAndExperience.AddFilePoints(profile.Files, model.ProfessionalFileCodeFellowshipCertificate, 1)
			case model.ProfessionalPreferredGradeSpecialist:
				resp.WorkPreferencesAndExperience.AddFilePoints(profile.Files, model.ProfessionalFileCodeSpecialistQualification, 1)
			}
		}
	}
	resp.WorkPreferencesAndExperience.AddPoints(professional.ExperienceLevel)
	// MedicationEndorsement 僅對 Enrolled Nurse 職業必填
	if professional.Profession == model.ProfessionalProfessionEnrolledNurse {
		resp.WorkPreferencesAndExperience.AddPoints(professional.MedicationEndorsement)
	}

	// 學歷資格證書上傳 - 只有Medical Practitioner、Registered Nurse、Enrolled Nurse需要
	if professional.Profession == model.ProfessionalProfessionMedicalPractitioner ||
		professional.Profession == model.ProfessionalProfessionRegisteredNurse ||
		professional.Profession == model.ProfessionalProfessionEnrolledNurse {
		resp.WorkPreferencesAndExperience.AddFilePoints(profile.Files, model.ProfessionalFileCodeQualificationCertificate, 1)

		// 畢業院校相關字段統計
		resp.WorkPreferencesAndExperience.AddPoints(profile.QualificationName)
		resp.WorkPreferencesAndExperience.AddPoints(profile.GraduationInstitution)
		resp.WorkPreferencesAndExperience.AddPoints(profile.InstitutionCountry)
		resp.WorkPreferencesAndExperience.AddPoints(professional.GraduationYear > 0)
	}

	// 確認CV無聯絡方式
	resp.WorkPreferencesAndExperience.AddPoints(profile.CvNoContactDetailsConfirmed == "Y")
	resp.WorkPreferencesAndExperience.AddFilePoints(profile.Files, model.ProfessionalFileCodeCurriculumVitae, 1)

	if profile.CompletedStudiesInLastThreeYears == "Y" {
		resp.WorkPreferencesAndExperience.AddPoints(profile.Qualification)
		resp.WorkPreferencesAndExperience.AddPoints(profile.QualificationEndDate)
	}

	if len(profile.Experiences) == 0 {
		resp.WorkPreferencesAndExperience.AddPoints(false)
	} else {
		for _, experience := range profile.Experiences {
			endDateValid := experience.StillInRole == "Y" || experience.EndDate != ""
			baseValid := experience.FacilityName != "" && experience.Speciality != "" && experience.StartDate != "" && experience.AdditionalNotes != "" && endDateValid

			itemValid := false
			switch {
			case !baseValid:
				itemValid = false
			case professional.Profession == model.ProfessionalProfessionMedicalPractitioner && experience.SubSpeciality != "" && experience.Grade != "":
				itemValid = true
			case experience.Role != "" && (experience.Role != model.ProfessionalExperienceRoleOther || experience.RoleOtherName != ""):
				itemValid = true
			}
			resp.WorkPreferencesAndExperience.AddPoints(itemValid)
		}
	}

	validReference := 0 // 是否有有效的推薦人
	for _, reference := range profile.References {
		if reference.ReferenceFirstName != "" && reference.ReferenceLastName != "" && reference.DateWorkedTogetherBegin != "" && reference.ReferenceEmail != "" && reference.ReferenceRole != "" && reference.ReferenceFacility != "" {
			validReference += 1
		} else {
			resp.WorkPreferencesAndExperience.HasEmpty = true
		}
	}
	resp.WorkPreferencesAndExperience.AddPoints(profile.ReferencesTermsConfirm == "Y")
	// 必須有2個完整推薦人
	resp.WorkPreferencesAndExperience.AddPoints(validReference == 2)

	// 註冊及證書
	resp.RegistrationAndCertification.AddPoints(profile.SoleTrader)
	resp.RegistrationAndCertification.AddFilePoints(profile.Files, model.ProfessionalFileCodeAhpraCertificate, 1)
	resp.RegistrationAndCertification.AddFilePoints(profile.Files, model.ProfessionalFileCodeIndemnityInsuranceCertificate, 1)
	if ok, err := s.CheckAbnStatus(professional, profile); err != nil {
		return resp, err
	} else {
		resp.RegistrationAndCertification.AddPoints(ok)
	}

	// 身份證明文件及記錄
	resp.ProofOfIdentityAndRecords.AddPoints(profile.IdCheckDeletionConsent == model.ProfessionalIdCheckDeletionConsentY)
	resp.ProofOfIdentityAndRecords.AddPoints(profile.WorkingWithChildrenOrVulnerablePeopleStates != "") // 兒童/脆弱人群工作資格地區（必填）
	if professional.PermissionToWork == model.ProfessionalPermissionToWorkVisa {
		resp.ProofOfIdentityAndRecords.AddFilePoints(profile.Files, model.ProfessionalFileCodeVisa, 1)
	}
	resp.ProofOfIdentityAndRecords.AddFilePoints(profile.Files, model.ProfessionalFileCodeNationalCriminalCheck, 1)
	resp.ProofOfIdentityAndRecords.AddFilePoints(profile.Files, model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople, 1)
	resp.ProofOfIdentityAndRecords.AddFilePoints(profile.Files, model.ProfessionalFileCodeCurrentImmunisationRecords, 1)
	resp.ProofOfIdentityAndRecords.AddPoints(profile.HasOverseasCitizenshipOrPr)
	if profile.HasOverseasCitizenshipOrPr == "Y" {
		resp.ProofOfIdentityAndRecords.AddFilePoints(profile.Files, model.ProfessionalFileCodeCommonwealthStatutoryDeclaration, 1)
	}
	idCheckScore, err := s.GetIdCheckScore(profile.Files)
	if err != nil {
		return resp, err
	}
	idCheckPassed := true
	if idCheckScore.Total < 100 || (idCheckScore.Primary == 0 && idCheckScore.Secondary == 0) {
		idCheckPassed = false
	}
	resp.ProofOfIdentityAndRecords.AddPoints(idCheckPassed)

	// 附加證明
	resp.AdditionalInformation.AddPoints(profile.HasCompletedInfectionControlTraining == "Y")
	if profile.HasCompletedInfectionControlTraining == "Y" {
		resp.AdditionalInformation.AddFilePoints(profile.Files, model.ProfessionalFileCodeAdditionalCertification, 0)
	}

	needDisclosureFile := false
	for _, question := range profile.DisclosureQuestions {
		resp.AdditionalInformation.AddPoints(question.Answer)
		if question.Answer == "Y" {
			needDisclosureFile = true
		}
	}
	if needDisclosureFile {
		resp.AdditionalInformation.AddFilePoints(profile.Files, model.ProfessionalFileCodeDisclosure, 1)
	}
	resp.AdditionalInformation.AddFilePoints(profile.Files, model.ProfessionalFileCodeSignedAgreement, 1)
	if resp.AdditionalInformation.Current > 0 {
		// AdditionalInformation 有進度時才檢查，避免沒填寫就顯示進度問題
		// 檢查披露問題數量
		disclosureCodes, err := SelectionService.GetCodeByType(db, model.SelectionTypeProfessionalDisclosureQuestion, model.SelectionStatusEnable)
		if err != nil {
			return resp, err
		}
		if len(disclosureCodes) != len(profile.DisclosureQuestions) {
			resp.AdditionalInformation.AddPoints(false)
		}
	}

	// 合計
	resp.Total = resp.PersonalInformation.Total + resp.WorkPreferencesAndExperience.Total + resp.RegistrationAndCertification.Total + resp.ProofOfIdentityAndRecords.Total + resp.AdditionalInformation.Total
	resp.Current = resp.PersonalInformation.Current + resp.WorkPreferencesAndExperience.Current + resp.RegistrationAndCertification.Current + resp.ProofOfIdentityAndRecords.Current + resp.AdditionalInformation.Current
	resp.Progress = resp.Current * 100 / resp.Total
	resp.HasEmpty = resp.PersonalInformation.HasEmpty || resp.WorkPreferencesAndExperience.HasEmpty || resp.RegistrationAndCertification.HasEmpty || resp.ProofOfIdentityAndRecords.HasEmpty || resp.AdditionalInformation.HasEmpty
	return resp, nil
}

//endregion ---------------------------------------------------- 獲取專業人士資料進度 ----------------------------------------------------

//region ---------------------------------------------------- IdCheck ----------------------------------------------------

const (
	ProfessionalProfileIdCheckFilePrimary   = "PRIMARY_FILE"
	ProfessionalProfileIdCheckFileSecondary = "SECONDARY_FILE"
	ProfessionalProfileIdCheckFileOther     = "OTHER_FILE"
)

var professionalProfileIdCheckFileMap = map[string]string{
	model.ProfessionalFileCodeAustralianPassport:                    ProfessionalProfileIdCheckFilePrimary,
	model.ProfessionalFileCodeForeignPassport:                       ProfessionalProfileIdCheckFilePrimary,
	model.ProfessionalFileCodeAustralianBirthCertificate:            ProfessionalProfileIdCheckFilePrimary,
	model.ProfessionalFileCodeAustralianCitizenshipCertificate:      ProfessionalProfileIdCheckFilePrimary,
	model.ProfessionalFileCodeCurrentAustraliaDriverLicence:         ProfessionalProfileIdCheckFileSecondary,
	model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard: ProfessionalProfileIdCheckFileSecondary,
	model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard:  ProfessionalProfileIdCheckFileSecondary,
	model.ProfessionalFileCodeTertiaryStudentIDCard:                 ProfessionalProfileIdCheckFileSecondary,
	model.ProfessionalFileCodeCreditDebitAtmCard:                    ProfessionalProfileIdCheckFileOther,
	model.ProfessionalFileCodeMedicareCard:                          ProfessionalProfileIdCheckFileOther,
	model.ProfessionalFileCodeUtilityBillOrRateNotice:               ProfessionalProfileIdCheckFileOther,
	model.ProfessionalFileCodeStatementFromFinancialInstitution:     ProfessionalProfileIdCheckFileOther,
	model.ProfessionalFileCodeCentrelinkOrPensionCard:               ProfessionalProfileIdCheckFileOther,
}
var ProfessionalProfileIdCheckFileScoreMap = map[string]int{
	ProfessionalProfileIdCheckFilePrimary:   70,
	ProfessionalProfileIdCheckFileSecondary: 25, // 第一次副文件加15分(40分)
	ProfessionalProfileIdCheckFileOther:     25,
}

type ProfessionalProfileIdCheckResp struct {
	Total     int `json:"total"`     // 總數
	Primary   int `json:"primary"`   // 主文件分數
	Secondary int `json:"secondary"` // 副文件分數
	Other     int `json:"other"`     // 其他分數
}

func (s *professionalProfileService) GetIdCheckScore(files []model.ProfessionalProfileFile) (ProfessionalProfileIdCheckResp, error) {
	var resp ProfessionalProfileIdCheckResp
	firstSecondary := 15 // 第一次副文件加15分(40分)
	for _, file := range files {
		var category string
		var exists bool
		if category, exists = professionalProfileIdCheckFileMap[file.FileCode]; !exists {
			continue
		}
		if file.ExpiryDate == "" {
			continue
		}
		score := ProfessionalProfileIdCheckFileScoreMap[category]
		switch category {
		case ProfessionalProfileIdCheckFilePrimary:
			resp.Primary += score
		case ProfessionalProfileIdCheckFileSecondary:
			score += firstSecondary
			firstSecondary = 0 // 只加一次
			resp.Secondary += score
		case ProfessionalProfileIdCheckFileOther:
			resp.Other += score
		}
		resp.Total += score
	}
	return resp, nil
}

//endregion ---------------------------------------------------- IdCheck ----------------------------------------------------

// TODO 沒有更新版本號
// region ---------------------------------------------------- 增加版本 ----------------------------------------------------
func (s *professionalProfileService) IncrementVersion(currentVersion string, majorDelta, minorDelta int) (string, error) {
	versionWithoutPrefix := strings.TrimPrefix(currentVersion, "v")
	versionParts := strings.Split(versionWithoutPrefix, ".")
	if len(versionParts) != 2 {
		return "", fmt.Errorf("invalid version format: %s", currentVersion)
	}

	major, err := strconv.Atoi(versionParts[0])
	if err != nil {
		return "", fmt.Errorf("invalid major version: %w", err)
	}

	minor, err := strconv.Atoi(versionParts[1])
	if err != nil {
		return "", fmt.Errorf("invalid minor version: %w", err)
	}

	return fmt.Sprintf("v%d.%d", major+majorDelta, minor+minorDelta), nil
}

//endregion ---------------------------------------------------- 增加版本 ----------------------------------------------------

// region ---------------------------------------------------- 提交審核 ----------------------------------------------------

type ProfessionalProfileSubmitReq struct {
	ProfessionalId uint64   `json:"professionalId" binding:"required"`        // 專業人士ID
	UpdatePrompt   string   `json:"updatePrompt" binding:"omitempty,max=150"` // 更新提示
	Files          []uint64 `json:"files" binding:"max=3"`                    // 文件ID
	OnlyCheck      string   `json:"onlyCheck" binding:"omitempty,oneof=Y"`    // 僅檢查
}

func (s *professionalProfileService) ProfessionalProfileSubmit(db *gorm.DB, req ProfessionalProfileSubmitReq, professional model.Professional) error {
	nowTime := time.Now().UTC().Truncate(time.Second)
	var updateMap = map[string]interface{}{
		"status":           model.ProfessionalStatusReviewing,
		"application_time": &nowTime,
		"update_prompt":    req.UpdatePrompt,
		"reject_reason":    "",
	}
	// 生成推薦人表單
	if err := s.GenReferenceForm(db, req.ProfessionalId); err != nil {
		return err
	}
	if err := db.Model(&model.Professional{}).
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		Where("id = ?", req.ProfessionalId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// 刪除更新文件關聯
	if err := s.DeleteUpdatePromptFiles(db, professional); err != nil {
		return err
	}

	// 新增更新文件關聯
	if len(req.Files) > 0 {
		var files []model.ProfessionalFileRelation
		for _, fileId := range req.Files {
			files = append(files, model.ProfessionalFileRelation{
				UserId:             professional.UserId,
				ProfessionalId:     professional.Id,
				ProfessionalFileId: fileId,
			})
		}
		if err := db.Create(&files).Error; err != nil {
			return err
		}
	}
	return nil
}

// 需要在变 ProfessionalStatusReviewing 之前调用
func (s *professionalProfileService) GenReferenceForm(db *gorm.DB, professionalId uint64) error {
	var professional model.Professional
	if err := db.
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		Where("id = ?", professionalId).
		First(&professional).Error; err != nil {
		return err
	}
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return err
	}
	var professionNameMap map[string]string
	professionNameMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return err
	}
	for i, reference := range profile.References {
		if reference.FormUuid != "" {
			// 之前已經生成了form
			continue
		}
		form := model.ProfessionalReferenceForm{}.DefaultForm(professionNameMap, professional, reference)
		form.MatchUuids = []string{uuid.NewV4().String()} // 用於電郵
		var formJson []byte
		formJson, err = json.Marshal(form)
		if err != nil {
			return err
		}
		profile.References[i].FormData = string(formJson)
		profile.References[i].FormUuid = uuid.NewV4().String()
		profile.References[i].FormStatus = model.ProfessionalProfileReferencesFormStatusPending
	}
	err = professional.MarshalProfile(profile)
	if err != nil {
		return err
	}
	// 通过指定字段只更新 ProfileJson
	if err := db.Model(&model.Professional{}).
		Where("id = ?", professional.Id).
		Updates(map[string]interface{}{
			"profile_json": professional.ProfileJson,
		}).Error; err != nil {
		return err
	}
	return nil
}

//endregion ---------------------------------------------------- 提交審核 ----------------------------------------------------

// region ---------------------------------------------------- 撤銷審核 ----------------------------------------------------

type ProfessionalProfileWithdrawReq struct {
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
}

func (s *professionalProfileService) ProfessionalProfileWithdraw(db *gorm.DB, req ProfessionalProfileWithdrawReq, professional model.Professional) error {
	var updateMap = map[string]interface{}{
		"status":           model.ProfessionalStatusPending,
		"application_time": xtype.NullString(),
		"update_prompt":    "",
		"reject_reason":    "",
	}
	if err := db.Model(&model.Professional{}).
		Where("data_type = ?", model.ProfessionalDataTypeDraft).
		Where("id = ?", req.ProfessionalId).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// 刪除更新文件關聯
	if err := s.DeleteUpdatePromptFiles(db, professional); err != nil {
		return err
	}
	return nil
}

//endregion ---------------------------------------------------- 撤銷審核 ----------------------------------------------------

func (s *professionalProfileService) DeleteUpdatePromptFiles(db *gorm.DB, professional model.Professional) error {
	// 清空更新文件關聯
	var fileIds []uint64
	if err := db.Model(&model.ProfessionalFileRelation{}).
		Table("professional_file_relation AS pfr").
		Joins("JOIN professional_file AS f ON pfr.professional_file_id = f.id").
		Where("pfr.professional_id = ?", professional.Id).
		Where("f.file_code = ?", model.ProfessionalFileCodeUpdatePrompt).
		Pluck("pfr.professional_file_id", &fileIds).Error; err != nil {
		return err
	}
	if len(fileIds) > 0 {
		if err := db.Model(&model.ProfessionalFileRelation{}).
			Where("professional_id = ?", professional.Id).
			Where("user_id = ?", professional.UserId).
			Where("professional_file_id IN (?)", fileIds).
			Delete(&model.ProfessionalFileRelation{}).Error; err != nil {
			return err
		}
		if err := db.Model(&model.ProfessionalFile{}).
			Where("user_id = ?", professional.UserId).
			Where("file_code = ?", model.ProfessionalFileCodeUpdatePrompt).
			Where("id IN ?", fileIds).
			Delete(&model.ProfessionalFile{}).Error; err != nil {
			return err
		}
	}
	return nil
}

// region ---------------------------------------------------- 檢查ABN ----------------------------------------------------

type ProfessionalProfileCheckAbnReq struct {
	ProfessionalId uint64 `form:"professionalId" binding:"required"`          // 專業人士ID
	AbnNumber      string `form:"abnNumber" binding:"required,min=11,max=11"` // ABN號碼
}

type ProfessionalProfileCheckAbnResp struct {
	Valid string `json:"valid"` // 是否有效 Y:有效 N:無效
	Match string `json:"match"` // 是否匹配 Y:匹配 N:不匹配
}

func (s *professionalProfileService) ProfessionalProfileCheckAbn(db *gorm.DB, req ProfessionalProfileCheckAbnReq) (ProfessionalProfileCheckAbnResp, error) {
	var err error
	resp := ProfessionalProfileCheckAbnResp{
		Valid: "N",
		Match: "N",
	}

	var abnInfo AbnInfo
	ok, _, err := s.CheckAbn(db, req.AbnNumber, &abnInfo, nil)
	if err != nil {
		return resp, err
	}
	if ok {
		professional, _, err := s.GetProfessionalProfile(db, req.ProfessionalId)
		if err != nil {
			return resp, err
		}
		resp.Valid = "Y"
		if ok = s.MatchAbnEntityName(abnInfo.EntityName, professional.FirstName, professional.LastName); ok {
			resp.Match = "Y"
		}
	}
	return resp, nil
}

//endregion ---------------------------------------------------- 檢查ABN ----------------------------------------------------

// region ---------------------------------------------------- 獲取付款資料 ----------------------------------------------------

type ProfessionalProfilePaymentDetailReq struct {
	UserId uint64 `form:"-" json:"-"` // 用戶ID
}

type ProfessionalProfilePaymentDetailReqBySystem struct {
	UserId uint64 `form:"userId" binding:"required"` // 用戶ID
}

type ProfessionalProfilePaymentDetailResp struct {
	BankAccountName   string `json:"bankAccountName"`   // 銀行帳戶名稱
	BankStateBranch   string `json:"bankStateBranch"`   // 銀行州分行
	BankAccountNumber string `json:"bankAccountNumber"` // 銀行帳戶號碼
}

func (s *professionalProfileService) ProfessionalProfilePaymentDetail(db *gorm.DB, req ProfessionalProfilePaymentDetailReq) (ProfessionalProfilePaymentDetailResp, error) {
	var resp ProfessionalProfilePaymentDetailResp
	if err := db.Model(&model.ProfessionalBankAccount{}).
		Where("user_id = ?", req.UserId).
		Select([]string{"bank_account_name", "bank_state_branch", "bank_account_number"}).
		First(&resp).Error; xgorm.IsSqlErr(err) {
		return resp, err
	}
	return resp, nil
}

//endregion ---------------------------------------------------- 獲取付款資料 ----------------------------------------------------

// region ---------------------------------------------------- 更新付款資料 ----------------------------------------------------

type ProfessionalProfileUpdatePaymentDetailReq struct {
	UserId            uint64 `swaggerignore:"true" json:"-"`                        // 用戶ID
	BankAccountName   string `json:"bankAccountName" binding:"omitempty,max=255"`   // 銀行帳戶名稱
	BankStateBranch   string `json:"bankStateBranch" binding:"omitempty,max=255"`   // 銀行州分行
	BankAccountNumber string `json:"bankAccountNumber" binding:"omitempty,max=255"` // 銀行帳戶號碼
}

func (s *professionalProfileService) ProfessionalProfileUpdatePaymentDetail(db *gorm.DB, req ProfessionalProfileUpdatePaymentDetailReq) error {
	updateMap := map[string]interface{}{
		"bank_account_name":   req.BankAccountName,
		"bank_state_branch":   req.BankStateBranch,
		"bank_account_number": req.BankAccountNumber,
	}
	if err := db.Model(&model.ProfessionalBankAccount{}).
		Where("user_id = ?", req.UserId).
		Updates(updateMap).Error; err != nil {
		return err
	}
	return nil
}

//endregion ---------------------------------------------------- 更新付款資料 ----------------------------------------------------

// 對比ABN實體名稱和專業人士姓名
func (s *professionalProfileService) MatchAbnEntityName(abnEntityName, firstName, lastName string) bool {
	// 檢查實體名稱是否匹配
	entityParts := strings.Split(strings.ToLower(abnEntityName), ",")
	if len(entityParts) >= 2 {
		first := strings.ToLower(firstName)
		last := strings.ToLower(lastName)

		lastNameFromAbn := strings.TrimSpace(entityParts[0])
		givenNames := strings.Fields(strings.TrimSpace(entityParts[1])) // fields 會自動去除多個空白
		if len(givenNames) > 0 && first == givenNames[0] && last == lastNameFromAbn {
			return true
		}
	}

	return false
}

func (s *professionalProfileService) CheckAbnStatus(professional model.Professional, profile model.ProfessionalProfile) (bool, error) {
	// 檢查 ABN 號碼是否為空
	if professional.AbnNumber == "" {
		return false, nil
	}
	matched := false
	matched = s.MatchAbnEntityName(professional.AbnEntityName, professional.FirstName, professional.LastName)

	if profile.SoleTrader == "Y" {
		return matched, nil
	}
	if !matched && professional.AbnValid != "Y" {
		return false, nil
	}
	// 檢查是否有 ABN 文件
	for _, file := range profile.Files {
		if file.FileCode == model.ProfessionalFileCodeAbn && len(file.ProfessionalFileIds) > 0 {
			return true, nil
		}
	}

	return false, nil
}

// region ---------------------------------------------------- 獲取專業人士列表 ----------------------------------------------------

type ProfessionalProfileListReq struct {
	DataType             string `form:"dataType" binding:"required,oneof=DRAFT APPROVED"`                          // 資料類型 DRAFT=草稿 APPROVED=已審核
	Gender               string `form:"gender" binding:"omitempty,oneof=FEMALE MALE NON_BINARY PREFER_NOT_TO_SAY"` // 性別 FEMALE=女 MALE=男 NON_BINARY=非二元 PREFER_NOT_TO_SAY=不願意說
	Name                 string `form:"name"`                                                                      // 姓名
	Email                string `form:"email"`                                                                     // 電子郵件
	Profession           string `form:"profession" binding:"max=255"`                                              // 專業
	ExperienceLevel      string `form:"experienceLevel" binding:"max=255"`                                         // 經驗等級
	Status               string `form:"status" binding:"required,oneof=PENDING REVIEWING APPROVED"`                // 狀態 PENDING=待審核 REVIEWING=審核中 APPROVED=已審核
	AccountStatus        string `form:"accountStatus" binding:"omitempty,oneof=ENABLE DISABLE"`                    // 帳號狀態 ENABLE=啟用 DISABLE=停用
	BeginApplicationDate string `form:"beginApplicationDate" binging:"omitempty,datetime=2006-01-02"`              // 開始申請日期
	EndApplicationDate   string `form:"endApplicationDate" binging:"omitempty,datetime=2006-01-02"`                // 結束申請日期
	ReferenceFormStatus  string `form:"referenceFormStatus" binding:"omitempty,oneof=UNFILLED FILLED"`             // 推薦人表單狀態 UNFILLED=未全部填寫 FILLED=已全部填寫
}

type ProfessionalProfileListResp struct {
	ProfessionalId       uint64     `json:"professionalId"`               // 專業人士ID
	UserId               uint64     `json:"userId"`                       // 用戶ID
	DataType             string     `json:"dataType"`                     // 資料類型
	FirstName            string     `json:"firstName"`                    // 名字
	LastName             string     `json:"lastName"`                     // 姓氏
	Email                string     `json:"email"`                        // 電子郵件
	Gender               string     `json:"gender"`                       // 性別
	Profession           string     `json:"profession"`                   // 專業
	ExperienceLevel      string     `json:"experienceLevel"`              // 經驗等級
	Status               string     `json:"status"`                       // 狀態
	AccountStatus        string     `json:"accountStatus"`                // 帳號狀態 ENABLE=啟用 DISABLE=停用
	BeginApplicationDate string     `json:"beginApplicationDate"`         // 開始申請日期
	EndApplicationDate   string     `json:"endApplicationDate"`           // 結束申請日期
	ApplicationTime      *time.Time `json:"applicationTime"`              // 申請時間
	ApprovedTime         *time.Time `json:"approvedTime"`                 // 審核時間
	RejectReason         string     `json:"rejectReason"`                 // 拒絕原因
	ProfileJson          string     `json:"-"`                            // 專業人士資料
	PhotoFileId          uint64     `json:"photoFileId" gorm:"-"`         // 照片文件Id
	GenderName           string     `json:"genderName" gorm:"-"`          // 性別名稱
	ProfessionName       string     `json:"professionName" gorm:"-"`      // 專業名稱
	ExperienceLevelName  string     `json:"experienceLevelName" gorm:"-"` // 經驗等級名稱
	ReferenceFormStatus  string     `json:"referenceFormStatus"`          // 推薦人表單狀態
}

func (s *professionalProfileService) List(db *gorm.DB, req ProfessionalProfileListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]ProfessionalProfileListResp, error) {
	var err error
	var resp []ProfessionalProfileListResp
	builder := db.Table("professional AS p").
		Joins("JOIN user AS u ON p.user_id = u.id").
		Select([]string{
			"p.id AS professional_id",
			"p.user_id",
			"p.data_type",
			"p.first_name",
			"p.last_name",
			"u.email",
			"p.gender",
			"p.profession",
			"p.experience_level",
			"p.status",
			"u.status AS account_status",
			"p.application_time",
			"p.approved_time",
			"p.reject_reason",
			"p.profile_json",
			"p.reference_form_status",
		})
	if req.DataType != "" {
		builder = builder.Where("p.data_type = ?", req.DataType)
	}
	if req.Name != "" {
		builder = builder.Where("CONCAT(p.first_name, ' ', p.last_name) LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Profession != "" {
		builder = builder.Where("p.profession = ?", req.Profession)
	}
	if req.ExperienceLevel != "" {
		builder = builder.Where("p.experience_level = ?", req.ExperienceLevel)
	}
	if req.Gender != "" {
		builder = builder.Where("p.gender = ?", req.Gender)
	}
	if req.Status != "" {
		builder = builder.Where("p.status = ?", req.Status)
	}
	if req.AccountStatus != "" {
		builder = builder.Where("u.status = ?", req.AccountStatus)
	}
	if req.Email != "" {
		builder = builder.Where("u.email LIKE ?", xgorm.EscapeLikeWithWildcards(req.Email))
	}
	if req.BeginApplicationDate != "" {
		builder = builder.Where("DATE(p.application_time) >= ?", req.BeginApplicationDate)
	}
	if req.EndApplicationDate != "" {
		builder = builder.Where("DATE(p.application_time) <= ?", req.EndApplicationDate)
	}
	if req.ReferenceFormStatus != "" {
		builder = builder.Where("p.reference_form_status = ?", req.ReferenceFormStatus)
	}
	sortKeyList := map[string]string{
		"name":            "CONCAT(p.first_name, ' ', p.last_name)",
		"applicationTime": "p.application_time",
		"approvedTime":    "p.approved_time",
	}
	if err = builder.Scopes(xresp.Paginate(pageSet)).Scopes(xresp.AddOrder(sortSet, sortKeyList)).Order("p.id").Find(&resp).Error; err != nil {
		return resp, err
	}

	var professionSectionMap map[string]string
	professionSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeProfessionalProfession})
	if err != nil {
		return resp, err
	}
	var experienceLevelSectionMap map[string]string
	experienceLevelSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeExperienceLevelMedicalPractitioner, model.SelectionTypeExperienceLevelRegisteredNurse})
	if err != nil {
		return resp, err
	}
	var genderSectionMap map[string]string
	genderSectionMap, err = SelectionService.GetCodeNameMap(db, []string{model.SelectionTypeGender})
	if err != nil {
		return resp, err
	}

	// 提取照片文件Id
	var professional model.Professional
	for i, item := range resp {
		if item.ProfileJson != "" {
			var profile model.ProfessionalProfile
			profile, err = professional.UnmarshalProfile(item.ProfileJson)
			if err != nil {
				return resp, err
			}
			for _, file := range profile.Files {
				if file.FileCode == model.ProfessionalFileCodePhoto {
					if len(file.ProfessionalFileIds) > 0 {
						resp[i].PhotoFileId = file.ProfessionalFileIds[0]
						break
					}
				}
			}
		}
		resp[i].ProfessionName = professionSectionMap[item.Profession]
		resp[i].ExperienceLevelName = experienceLevelSectionMap[item.ExperienceLevel]
		resp[i].GenderName = genderSectionMap[item.Gender]
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 獲取專業人士列表 ----------------------------------------------------
func (s *professionalProfileService) GetProfessionSpecialitiesTree(db *gorm.DB, specialities string) (map[string][]SelectionListResp, error) {
	var err error
	selectionTypes := []string{
		model.SelectionTypePreferredSpecialityMedicalPractitioner,
		model.SelectionTypePreferredSpecialityNurse,
		model.SelectionTypePreferredSpecialityPersonalCareWorker,
	}

	selectionMap, err := SelectionService.List(db, SelectionListReq{
		SelectionTypes: strings.Join(selectionTypes, ","),
	})
	if err != nil {
		return nil, err
	}
	selectedSpecialitiesArr := strings.Split(specialities, ",")
	selectedSpecialitiesMap := map[string]bool{}
	for _, p := range selectedSpecialitiesArr {
		selectedSpecialitiesMap[p] = true
	}
	resultMap := make(map[string][]SelectionListResp)
	for firstCode, first := range selectionMap {
		selectedFirst := make([]SelectionListResp, 0)
		for _, item := range first {
			if item.Children != nil {
				selectedSecond := make([]SelectionListResp, 0)
				for _, p := range *item.Children {
					if _, ok := selectedSpecialitiesMap[p.Code]; ok {
						selectedSecond = append(selectedSecond, p)
					}
				}
				if len(selectedSecond) > 0 {
					item.Children = &selectedSecond
				} else {
					item.Children = nil
				}
			}
			if _, ok := selectedSpecialitiesMap[item.Code]; ok || (item.Children != nil && len(*item.Children) > 0) {
				selectedFirst = append(selectedFirst, item)
			}
			if len(selectedFirst) > 0 {
				resultMap[item.SelectionType] = append(resultMap[item.SelectionType], item)
			}
		}
		if len(selectedFirst) > 0 {
			resultMap[firstCode] = selectedFirst
		}
	}
	return resultMap, nil

}

// region ---------------------------------------------------- 審核專業人士資料 ----------------------------------------------------

type ProfessionalProfileApproveReq struct {
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
	Approve        string `json:"approve" binding:"required,oneof=Y N"`                  // Y=通過,N=拒絕
	RejectReason   string `json:"rejectReason" binding:"required_if=Approve N,max=1024"` // 拒絕原因
	ReqUserId      uint64 `json:"-"`
}

func (s *professionalProfileService) Approve(db *gorm.DB, req ProfessionalProfileApproveReq) error {
	var err error
	var professional model.Professional
	if err = db.Where("data_type = ?", model.ProfessionalDataTypeDraft).
		Where("id = ?", req.ProfessionalId).
		Where("status = ?", model.ProfessionalStatusReviewing).
		First(&professional).Error; err != nil {
		return err
	}
	professional.UpdatePrompt = ""
	nowTime := time.Now().UTC().Truncate(time.Second)
	professional.ApprovedTime = &nowTime
	professional.ApprovedUserId = req.ReqUserId
	if req.Approve == "Y" {
		professional.Status = model.ProfessionalStatusPending
		professional.RejectReason = ""
		// 清除所有ID Check文件
		if err = s.ClearAllIdCheckFiles(db, &professional); err != nil {
			return err
		}
	} else {
		professional.Status = model.ProfessionalStatusPending
		professional.RejectReason = req.RejectReason
	}
	if err = db.Save(&professional).Error; err != nil {
		return err
	}

	// 刪除更新文件關聯
	if err = s.DeleteUpdatePromptFiles(db, professional); err != nil {
		return err
	}

	// TODO: 發送審核結果通知
	if req.Approve == "Y" {
		// Profile資料已經審核通過，完成培訓後可以開始找工作 - 通知Professional
		// 發送資料審核通過通知給專業人士
		ok, _, err := TrainingService.CheckProfessionalModuleNotCompleted(db, professional.UserId)
		if err != nil {
			return err
		}
		if !ok {
			// 如果培訓未完成，則發送通知
			if err = SystemNotificationService.CreateProfessionalProfileApproved(db, CreateProfessionalProfileApprovedReq{
				UserId:        professional.UserId,
				CreatorUserId: req.ReqUserId,
			}); err != nil {
				return err
			}
		}

		// Profile資料已經審核通過，如果ABN是sole trader，提示要去完善super資料 - 通知Professional
		// 檢查ABN類型，如果是sole trader則發送super資料完善提醒
		if professional.AbnNumber != "" && professional.AbnEntityType == "Individual/Sole Trader" { // Individual (Sole Trader)
			// 檢查是否已經完成super設定
			var superCount int64
			if err = db.Model(&model.ProfessionalSuperannuation{}).
				Where("user_id = ?", professional.UserId).
				Where("data_type = ?", model.ProfessionalSuperannuationDataTypeSubmitted).
				Where("submitted_time IS NOT NULL").
				Count(&superCount).Error; err != nil {
				return err
			}
			if superCount == 0 {
				if err = SystemNotificationService.CreateProfessionalProfileNeedSuper(db, CreateProfessionalProfileNeedSuperReq{
					UserId:        professional.UserId,
					AbnType:       professional.AbnEntityType,
					CreatorUserId: req.ReqUserId,
				}); err != nil {
					return err
				}
			}
		}
	} else {
		// Profile資料被駁回 - 通知Professional
		// 發送資料審核駁回通知給專業人士
		if err = SystemNotificationService.CreateProfessionalProfileRejected(db, CreateProfessionalProfileRejectedReq{
			UserId:          professional.UserId,
			RejectionReason: req.RejectReason,
			CreatorUserId:   req.ReqUserId,
		}); err != nil {
			return err
		}
	}

	// 如果通過，創建已審核版本
	if req.Approve == "Y" {
		// 將之前的 Approved 設為 History
		if err = db.Model(&model.Professional{}).
			Where("user_id = ?", professional.UserId).
			Where("data_type = ?", model.ProfessionalDataTypeApproved).
			Updates(map[string]interface{}{
				"data_type": model.ProfessionalDataTypeHistory,
			}).Error; err != nil {
			return err
		}

		var newProfile model.Professional
		_ = copier.Copy(&newProfile, professional)
		newProfile.Id = 0
		newProfile.DataType = model.ProfessionalDataTypeApproved
		newProfile.Status = model.ProfessionalStatusApproved
		if err = db.Create(&newProfile).Error; err != nil {
			return err
		}
		// 複製文件關係
		var files []model.ProfessionalFileRelation
		if err = db.Where("professional_id = ?", professional.Id).Find(&files).Error; err != nil {
			return err
		}
		for _, file := range files {
			newFileRelation := model.ProfessionalFileRelation{
				UserId:             newProfile.UserId,
				ProfessionalId:     newProfile.Id,
				ProfessionalFileId: file.ProfessionalFileId,
			}
			if err = db.Create(&newFileRelation).Error; err != nil {
				return err
			}
		}

	}

	return nil
}

// endregion ---------------------------------------------------- 審核專業人士資料 ----------------------------------------------------

// region ---------------------------------------------------- 清除專業人士的所有id check文件 ----------------------------------------------------

// 清除專業人士的所有ID Check文件
func (s *professionalProfileService) ClearAllIdCheckFiles(db *gorm.DB, professional *model.Professional) error {
	var err error

	// 解析專業人士資料
	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return err
	}

	// 收集需要刪除的ID Check文件
	var idCheckFileIds []uint64
	var filesToDelete []model.ProfessionalFile

	for _, file := range profile.Files {
		// 檢查是否為ID Check相關文件類型
		if _, isIdCheckFile := professionalProfileIdCheckFileMap[file.FileCode]; isIdCheckFile {
			for _, fileId := range file.ProfessionalFileIds {
				idCheckFileIds = append(idCheckFileIds, fileId)
			}
		}
	}

	// 如果沒有ID Check文件，直接返回
	if len(idCheckFileIds) == 0 {
		return nil
	}

	// 查詢需要刪除的文件詳細信息（用於刪除OSS文件）
	if err = db.Where("user_id = ?", professional.UserId).
		Where("id IN (?)", idCheckFileIds).
		Find(&filesToDelete).Error; err != nil {
		return err
	}

	// 刪除文件關聯
	if err = db.Where("professional_id = ?", professional.Id).
		Where("professional_file_id IN (?)", idCheckFileIds).
		Delete(&model.ProfessionalFileRelation{}).Error; err != nil {
		return err
	}

	// 刪除文件記錄
	if err = db.Where("user_id = ?", professional.UserId).
		Where("id IN (?)", idCheckFileIds).
		Delete(&model.ProfessionalFile{}).Error; err != nil {
		return err
	}

	// 刪除OSS文件
	for _, file := range filesToDelete {
		// 刪除原文件
		if file.Path != "" {
			_ = xs3.DeleteObject(file.Bucket, file.Path)
		}
		// 刪除縮略圖
		if file.ThumbnailPath != "" {
			_ = xs3.DeleteObject(file.Bucket, file.ThumbnailPath)
		}
	}

	// 更新profile中的文件列表，移除ID Check文件
	var updatedFiles []model.ProfessionalProfileFile
	for _, file := range profile.Files {
		// 如果不是ID Check文件，保留
		if _, isIdCheckFile := professionalProfileIdCheckFileMap[file.FileCode]; !isIdCheckFile {
			updatedFiles = append(updatedFiles, file)
		}
	}
	profile.Files = updatedFiles

	// 更新專業人士的ProfileJson
	if err = professional.MarshalProfile(profile); err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 清除專業人士的所有id check文件 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士資料對比 ----------------------------------------------------

type ProfessionalProfileCompareReq struct {
	CurrentProfessionalId uint64 `form:"currentProfessionalId" binding:"required"` // 當前專業人士ID
	NewProfessionalId     uint64 `form:"newProfessionalId" binding:"required"`     // 新專業人士ID
}

type ProfessionalProfileCompareResp struct {
	PersonalInformation          []string `json:"personalInformation"`          // 個人資料
	WorkPreferencesAndExperience []string `json:"workPreferencesAndExperience"` // 工作偏好及經驗
	RegistrationAndCertification []string `json:"registrationAndCertification"` // 註冊及證書
	ProofOfIdentityAndRecords    []string `json:"proofOfIdentityAndRecords"`    // 身份證明及記錄
	AdditionalCertification      []string `json:"additionalCertification"`      // 附加證明
}

func (p *ProfessionalProfileCompareResp) Add(section string, msg string) {
	switch section {
	case "PersonalInformation":
		p.PersonalInformation = append(p.PersonalInformation, msg)
	case "WorkPreferencesAndExperience":
		p.WorkPreferencesAndExperience = append(p.WorkPreferencesAndExperience, msg)
	case "RegistrationAndCertification":
		p.RegistrationAndCertification = append(p.RegistrationAndCertification, msg)
	case "ProofOfIdentityAndRecords":
		p.ProofOfIdentityAndRecords = append(p.ProofOfIdentityAndRecords, msg)
	case "AdditionalCertification":
		p.AdditionalCertification = append(p.AdditionalCertification, msg)
	}
}

type ProfessionalProfileFieldAlert struct {
	Key string
	Msg i18n.Message
}

func (s *professionalProfileService) Compare(db *gorm.DB, req ProfessionalProfileCompareReq, lang string) (ProfessionalProfileCompareResp, error) {
	var fieldMap = map[string][]ProfessionalProfileFieldAlert{
		"PersonalInformation": {
			{Key: "FirstName", Msg: i18n.Message{ID: "professional.profile.field.first_name", Other: "First Name"}},                                                            // 名字
			{Key: "LastName", Msg: i18n.Message{ID: "professional.profile.field.last_name", Other: "Last Name"}},                                                               // 姓氏
			{Key: "PermissionToWork", Msg: i18n.Message{ID: "professional.profile.field.permission_to_work", Other: "Permission To Work"}},                                     // 工作許可
			{Key: "Profession", Msg: i18n.Message{ID: "professional.profile.field.profession", Other: "Profession"}},                                                           // 專業
			{Key: "Gender", Msg: i18n.Message{ID: "professional.profile.field.gender", Other: "Gender"}},                                                                       // 性別
			{Key: "DateOfBirth", Msg: i18n.Message{ID: "professional.profile.field.date_of_birth", Other: "Date Of Birth"}},                                                    // 出生日期
			{Key: "Address", Msg: i18n.Message{ID: "professional.profile.field.address", Other: "Address"}},                                                                    // 地址
			{Key: "AddressExtra", Msg: i18n.Message{ID: "professional.profile.field.address_extra", Other: "Address Extra"}},                                                   // 地址備註
			{Key: "PreferredState", Msg: i18n.Message{ID: "professional.profile.field.preferred_state", Other: "Preferred Location"}},                                          // 偏好州份
			{Key: "PreferredLocality", Msg: i18n.Message{ID: "professional.profile.field.preferred_locality", Other: "Preferred Location"}},                                    // 偏好縣市
			{Key: "DistanceWithin", Msg: i18n.Message{ID: "professional.profile.field.distance_within", Other: "Distance Within"}},                                             // 距離
			{Key: "MinimumHourlyRate", Msg: i18n.Message{ID: "professional.profile.field.minimum_hourly_rate", Other: "Minimum Hourly Rate"}},                                  // 最低時薪
			{Key: "EmergencyContactFirstName", Msg: i18n.Message{ID: "professional.profile.field.emergency_contact_first_name", Other: "Emergency Contact First Name"}},        // 緊急聯絡人名字
			{Key: "EmergencyContactLastName", Msg: i18n.Message{ID: "professional.profile.field.emergency_contact_last_name", Other: "Emergency Contact Last Name"}},           // 緊急聯絡人姓氏
			{Key: "EmergencyContactPhone", Msg: i18n.Message{ID: "professional.profile.field.emergency_contact_phone", Other: "Emergency Contact Phone"}},                      // 緊急聯絡人電話
			{Key: "EmergencyContactRelationship", Msg: i18n.Message{ID: "professional.profile.field.emergency_contact_relationship", Other: "Emergency Contact Relationship"}}, // 緊急聯絡人關係
		},
		"WorkPreferencesAndExperience": {
			{Key: "PreferredGrade", Msg: i18n.Message{ID: "professional.profile.field.preferred_grade", Other: "Preferred Grade"}},                                                               // 偏好等級
			{Key: "ExperienceLevel", Msg: i18n.Message{ID: "professional.profile.field.experience_level", Other: "Experience Level"}},                                                            // 經驗等級
			{Key: "PreferredSpecialities", Msg: i18n.Message{ID: "professional.profile.field.preferred_specialities", Other: "Preferred Specialities"}},                                          // 偏好專業 // TODO 這裡換名稱了
			{Key: "PreferredSpecialityOtherName", Msg: i18n.Message{ID: "professional.profile.field.preferred_speciality_other_name", Other: "Preferred Speciality Other Name"}},                 // 偏好專業其他名稱
			{Key: "Experiences", Msg: i18n.Message{ID: "professional.profile.field.experiences", Other: "Experiences"}},                                                                          // 經驗
			{Key: "CompletedStudiesInLastThreeYears", Msg: i18n.Message{ID: "professional.profile.field.completed_studies_in_last_three_years", Other: "Completed Studies In Last Three Years"}}, // 過去三年完成的學習
			{Key: "Qualification", Msg: i18n.Message{ID: "professional.profile.field.qualification", Other: "Qualification"}},                                                                    // 資格
			{Key: "QualificationEndDate", Msg: i18n.Message{ID: "professional.profile.field.qualification_end_date", Other: "Qualification End Date"}},                                           // 資格到期日
			{Key: "References", Msg: i18n.Message{ID: "professional.profile.field.references", Other: "References"}},                                                                             // 推薦人
		},
		"RegistrationAndCertification": {},
		"ProofOfIdentityAndRecords": {
			{Key: "HasOverseasCitizenshipOrPr", Msg: i18n.Message{ID: "professional.profile.field.has_overseas_citizenship_or_pr", Other: "Citizenship Since Age 16"}}, // 海外公民或永久居留
		},
		"AdditionalCertification": {
			{Key: "HasCompletedInfectionControlTraining", Msg: i18n.Message{ID: "professional.profile.field.has_completed_infection_control_training", Other: "CPR/BLS Certification"}}, // CPR 認證
			{Key: "DisclosureQuestions", Msg: i18n.Message{ID: "professional.profile.field.disclosure_questions", Other: "Disclosure Questions"}},                                       // 披露問題
		},
	}
	var fileMap = map[string][]ProfessionalProfileFieldAlert{
		"PersonalInformation": {
			{Key: model.ProfessionalFileCodePhoto, Msg: i18n.Message{ID: "professional.profile.field.photo", Other: "Photo"}}, // 照片
		},
		"WorkPreferencesAndExperience": {
			{Key: model.ProfessionalFileCodeIndemnityInsuranceCertificate, Msg: i18n.Message{ID: "professional.profile.field.indemnity_insurance_certificate", Other: "Indemnity Insurance Certificate"}}, // 責任保險證書
			// Personal Care Worker Qualifications 個人護理工作者資格證書
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIIndividualSupport, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iii_individual_support", Other: "Certificate III Individual Support"}},                 // 個人照顧工作者資格證書 III 個人支持
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIAGEDCARE, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iii_aged_care", Other: "Certificate III Aged Care"}},                                     // 個人照顧工作者資格證書 III 老年護理
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIIIHomeCommunityCare, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iii_home_community_care", Other: "Certificate III in Home and Community Care"}}, // 個人照顧工作者資格證書 III 家居社區護理
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertIIIDisabilities, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iii_disabilities", Other: "Certificate III in Disabilities"}},                               // 個人照顧工作者資格證書 III 殘疾人士
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertIVAgeingSupport, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iv_ageing_support", Other: "Certificate IV in Ageing Support"}},                             // 個人照顧工作者資格證書 IV 老年支持
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertIVDisabilities, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iv_disability", Other: "Certificate IV in Disability"}},                                      // 個人照顧工作者資格證書 IV 殘疾人士
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationCertificateIVHomeCommunityCare, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_certificate_iv_home_community_care", Other: "Certificate IV in Home and Community Care"}},    // 個人照顧工作者資格證書 IV 家居社區護理
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationBachelorNursing, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_bachelor_nursing", Other: "Bachelor of Nursing"}},                                                           // 個人照顧工作者資格證書 護理學士
			{Key: model.ProfessionalFileCodePersonalCareWorkerQualificationDiplomaNursing, Msg: i18n.Message{ID: "professional.profile.field.personal_care_worker_qualification_diploma_nursing", Other: "Diploma of Nursing"}},                                                              // 個人照顧工作者資格證書 護理文憑
		},
		"RegistrationAndCertification": {
			{Key: model.ProfessionalFileCodeAhpraCertificate, Msg: i18n.Message{ID: "professional.profile.field.ahpra_certificate", Other: "AHPRA Certificate"}}, // AHPRA 證書
			{Key: model.ProfessionalFileCodeAbn, Msg: i18n.Message{ID: "professional.profile.field.abn", Other: "ABN"}},                                          // ABN
		},
		"ProofOfIdentityAndRecords": {
			{Key: model.ProfessionalFileCodeAustralianPassport, Msg: i18n.Message{ID: "professional.profile.field.australian_passport", Other: "Australian Passport"}},                                                                       // 澳洲護照
			{Key: model.ProfessionalFileCodeForeignPassport, Msg: i18n.Message{ID: "professional.profile.field.foreign_passport", Other: "Foreign Passport"}},                                                                                // 外國護照
			{Key: model.ProfessionalFileCodeAustralianBirthCertificate, Msg: i18n.Message{ID: "professional.profile.field.australian_birth_certificate", Other: "Australian Birth Certificate"}},                                             // 澳洲出生證明
			{Key: model.ProfessionalFileCodeAustralianCitizenshipCertificate, Msg: i18n.Message{ID: "professional.profile.field.australian_citizenship_certificate", Other: "Australian Citizenship Certificate"}},                           // 澳洲公民證
			{Key: model.ProfessionalFileCodeCurrentAustraliaDriverLicence, Msg: i18n.Message{ID: "professional.profile.field.current_australia_driver_licence", Other: "Current Australia Driver Licence"}},                                  // 澳洲駕照
			{Key: model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard, Msg: i18n.Message{ID: "professional.profile.field.australian_public_service_employee_id_card", Other: "Australian Public Service Employee ID Card"}},      // 澳洲公務員ID卡
			{Key: model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard, Msg: i18n.Message{ID: "professional.profile.field.other_australian_government_issue_id_card", Other: "Other Australian Government Issue ID Card"}},         // 其他澳洲政府發出證書
			{Key: model.ProfessionalFileCodeTertiaryStudentIDCard, Msg: i18n.Message{ID: "professional.profile.field.tertiary_student_id_card", Other: "Tertiary Student ID Card"}},                                                          // 大學生ID卡
			{Key: model.ProfessionalFileCodeCreditDebitAtmCard, Msg: i18n.Message{ID: "professional.profile.field.credit_debit_atm_card", Other: "Credit Debit ATM Card"}},                                                                   // 信用卡/扣帳卡/ATM卡
			{Key: model.ProfessionalFileCodeMedicareCard, Msg: i18n.Message{ID: "professional.profile.field.medicare_card", Other: "Medicare Card"}},                                                                                         // 醫療卡
			{Key: model.ProfessionalFileCodeUtilityBillOrRateNotice, Msg: i18n.Message{ID: "professional.profile.field.utility_bill_or_rate_notice", Other: "Utility Bill Or Rate Notice"}},                                                  // 水電費單或收費通知
			{Key: model.ProfessionalFileCodeStatementFromFinancialInstitution, Msg: i18n.Message{ID: "professional.profile.field.statement_from_financial_institution", Other: "Statement From Financial Institution"}},                      // 財務機構結單
			{Key: model.ProfessionalFileCodeCentrelinkOrPensionCard, Msg: i18n.Message{ID: "professional.profile.field.centrelink_or_pension_card", Other: "Centrelink Or Pension Card"}},                                                    // 澳洲国民福利署或養老金卡
			{Key: model.ProfessionalFileCodeVisa, Msg: i18n.Message{ID: "professional.profile.field.visa", Other: "Visa"}},                                                                                                                   // 簽證
			{Key: model.ProfessionalFileCodeNationalCriminalCheck, Msg: i18n.Message{ID: "professional.profile.field.national_criminal_check", Other: "National Criminal Check"}},                                                            // 國家犯罪檢查
			{Key: model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople, Msg: i18n.Message{ID: "professional.profile.field.working_with_children_or_vulnerable_people", Other: "Working With Children / Vulnerable People Check"}}, // 兒童/脆弱人群工作檢查
			{Key: model.ProfessionalFileCodeCurrentImmunisationRecords, Msg: i18n.Message{ID: "professional.profile.field.current_immunisation_records", Other: "Current Immunisation Records"}},                                             // 現時免疫記錄
			{Key: model.ProfessionalFileCodeCommonwealthStatutoryDeclaration, Msg: i18n.Message{ID: "professional.profile.field.commonwealth_statutory_declaration", Other: "Commonwealth Statutory Declaration"}},                           // 澳洲聯邦法定聲明
		},
		"AdditionalCertification": {
			{Key: model.ProfessionalFileCodeAdditionalCertification, Msg: i18n.Message{ID: "professional.profile.field.additional_certification", Other: "Additional Certification"}}, // 附加證明
			{Key: model.ProfessionalFileCodeDisclosure, Msg: i18n.Message{ID: "professional.profile.field.disclosure", Other: "Disclosure"}},                                          // 披露
		},
	}

	resp := ProfessionalProfileCompareResp{
		PersonalInformation:          []string{},
		WorkPreferencesAndExperience: []string{},
		RegistrationAndCertification: []string{},
		ProofOfIdentityAndRecords:    []string{},
		AdditionalCertification:      []string{},
	}
	currentProfile, err := s.Inquire(db, ProfessionalProfileInquireReq{
		ProfessionalId: req.CurrentProfessionalId,
	})
	if err != nil {
		return resp, err
	}

	newProfile, err := s.Inquire(db, ProfessionalProfileInquireReq{
		ProfessionalId: req.NewProfessionalId,
	})
	if err != nil {
		return resp, err
	}

	// 使用指針來修改 resp
	// 遍歷fieldMap使用反射對比currentProfile，newProfile中的字段，如果不相同則將對應的Msg加入到resp中
	for section, fields := range fieldMap {
		for _, field := range fields {
			// 檢查字段是否存在
			oldValField := reflect.ValueOf(currentProfile).FieldByName(field.Key)
			newValField := reflect.ValueOf(newProfile).FieldByName(field.Key)

			// 如果字段不存在，跳過當前迭代
			if !oldValField.IsValid() || !newValField.IsValid() {
				continue
			}

			oldVal := oldValField.Interface()
			newVal := newValField.Interface()

			// 根據類型進行不同的比較
			var isDifferent bool
			switch oldVal.(type) {
			case string, int, int64, uint64, float64, bool:
				// 對於基本類型：字符串、數字等直接對比
				isDifferent = !reflect.DeepEqual(oldVal, newVal)
			default:
				// 對於其他類型：通過 JSON 序列化後對比
				oldJson, err := json.Marshal(oldVal)
				if err != nil {
					return resp, err
				}
				newJson, err := json.Marshal(newVal)
				if err != nil {
					return resp, err
				}
				isDifferent = !bytes.Equal(oldJson, newJson)
			}

			if isDifferent {
				msgStr := xi18n.LocalizeWithLang(lang, &field.Msg)
				resp.Add(section, msgStr)
			}
		}
	}

	// 處理文件比較
	for section, files := range fileMap {
		for _, file := range files {
			oldFiles, oldExists := currentProfile.Files[file.Key]
			newFiles, newExists := newProfile.Files[file.Key]

			msg := xi18n.LocalizeWithLang(lang, &file.Msg)

			if !oldExists && newExists && len(newFiles) > 0 {
				resp.Add(section, msg)
			} else if oldExists && !newExists {
				resp.Add(section, msg)
			} else if oldExists && newExists && len(oldFiles) > 0 && len(newFiles) > 0 {
				// 檢查文件是否有變化
				filesChanged := false

				// 判斷文件ID是否相同
				if len(oldFiles) != len(newFiles) {
					filesChanged = true
				} else {
					for i, oldFile := range oldFiles {
						if i >= len(newFiles) || !reflect.DeepEqual(oldFile.ProfessionalFiles, newFiles[i].ProfessionalFiles) {
							filesChanged = true
							break
						}

						// 檢查文件的其他屬性
						needParam := model.ProfessionalProfileFileNeedParamMap[file.Key]
						if needParam&model.ProfessionalProfileFileNeedExpiryDate != 0 && oldFile.ExpiryDate != newFiles[i].ExpiryDate {
							filesChanged = true
							break
						}
						if needParam&model.ProfessionalProfileFileNeedNumber != 0 && oldFile.Number != newFiles[i].Number {
							filesChanged = true
							break
						}
						if needParam&model.ProfessionalProfileFileNeedDescription != 0 && oldFile.Description != newFiles[i].Description {
							filesChanged = true
							break
						}
					}
				}

				if filesChanged {
					resp.Add(section, msg)
				}
			}
		}
	}

	// 將resp中的字段進行去重
	resp.PersonalInformation = xtool.StringArrayDeduplication(resp.PersonalInformation)
	resp.WorkPreferencesAndExperience = xtool.StringArrayDeduplication(resp.WorkPreferencesAndExperience)
	resp.RegistrationAndCertification = xtool.StringArrayDeduplication(resp.RegistrationAndCertification)
	resp.ProofOfIdentityAndRecords = xtool.StringArrayDeduplication(resp.ProofOfIdentityAndRecords)
	resp.AdditionalCertification = xtool.StringArrayDeduplication(resp.AdditionalCertification)

	return resp, nil
}

// endregion ---------------------------------------------------- 專業人士資料對比 ----------------------------------------------------

// region ---------------------------------------------------- 停用/啟用專業人士資料 ----------------------------------------------------

type ProfessionalProfileDeactivateReq struct {
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
	Status         string `json:"status" binding:"required,oneof=ENABLE DISABLE"` // ENABLE=啟用, DISABLE=停用
}

// 停用/啟用專業人士資料
func (s *professionalProfileService) Deactivate(db *gorm.DB, req ProfessionalProfileDeactivateReq) error {
	var err error

	// 獲取專業人士ID
	var professional model.Professional
	if err = db.Where("id = ?", req.ProfessionalId).First(&professional).Error; err != nil {
		return err
	}

	// 更新停用狀態
	if err = db.Model(&xmodel.User{}).
		Where("id = ?", professional.UserId).
		Updates(map[string]interface{}{
			"status": req.Status,
		}).Error; err != nil {
		return err
	}

	// 將設備更新為停用狀態
	if req.Status == xmodel.UserStatusDisable {
		if err = db.Model(&xmodel.UserDevice{}).
			Where("user_id = ?", professional.UserId).
			Updates(map[string]interface{}{
				"status": xmodel.UserDeviceStatusDisable,
			}).Error; err != nil {
			return err
		}
	}

	// 立即清除這些用戶的所有登入緩存 cache:user_device:userId:*
	_ = xredis.DeleteKey(db.Statement.Context, fmt.Sprintf("cache:user_device:%d:*", professional.UserId))

	return nil
}

// endregion ---------------------------------------------------- 停用/啟用專業人士資料 ----------------------------------------------------

func (s *professionalProfileService) CheckIsCompanyAbn(professional model.Professional) bool {
	if professional.AbnEntityType == "Individual/Sole Trader" {
		return false
	} else {
		return true
	}
}

func (s *professionalProfileService) GetPreferredSpecialities(Profession string, preferredSpecialities []model.ProfessionalPreferredSpeciality) []string {
	arr := make([]string, 0)
	for _, v := range preferredSpecialities {
		speciality := v.Speciality
		if Profession == model.ProfessionalProfessionMedicalPractitioner {
			speciality = v.SubSpeciality
		}
		if speciality != "" {
			arr = append(arr, speciality)
		}
	}
	return arr
}

// region ---------------------------------------------------- 審批專業人士的推薦人資料 ----------------------------------------------------

type ProfessionalProfileApproveReferenceReq struct {
	ProfessionalId uint64 `json:"professionalId" binding:"required"`
	FormUuid       string `json:"formUuid" binding:"required"`
	Status         string `json:"status" binding:"required,oneof=APPROVED REJECTED"` // APPROVED=啟用, REJECTED=停用
}

// 審批專業人士的推薦人資料
func (s *professionalProfileService) ApproveReference(db *gorm.DB, req ProfessionalProfileApproveReferenceReq) error {
	var err error

	// 獲取專業人士ID
	var professional model.Professional
	if err = db.Where("id = ?", req.ProfessionalId).First(&professional).Error; err != nil {
		return err
	}

	profile, err := professional.UnmarshalProfile(professional.ProfileJson)
	if err != nil {
		return err
	}

	for i, reference := range profile.References {
		if reference.FormUuid == req.FormUuid {
			profile.References[i].FormStatus = req.Status
		}
	}

	professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusFilled
	for _, reference := range profile.References {
		if reference.FormStatus == model.ProfessionalProfileReferencesFormStatusPending || reference.FormStatus == "" {
			professional.ReferenceFormStatus = model.ProfessionalReferenceFormStatusUnfilled
			break
		}
	}

	err = professional.MarshalProfile(profile)
	if err != nil {
		return err
	}

	if err = db.Save(&professional).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 審批專業人士的推薦人資料 ----------------------------------------------------
