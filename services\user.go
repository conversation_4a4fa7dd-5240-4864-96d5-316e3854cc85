package services

import (
	"fmt"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xcasbin"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

var UserService = new(userService)

type userService struct{}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查用戶是否存在
func (s *userService) CheckUserExists(db *gorm.DB, userType string, userId uint64, facilityId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.user.id.does_not_exist",
		Other: "Some thing went wrong, please try again later.",
	}
	var user xmodel.User
	builder := db.Model(&xmodel.User{}).Table("user AS u").Where("u.id = ?", userId)
	if userType != "" {
		builder = builder.Where("u.user_type = ?", userType)
	}
	if len(facilityId) > 0 {
		builder = builder.Joins("JOIN facility_user AS fu ON fu.user_id = u.id AND fu.facility_id = ?", facilityId[0])
	}
	if err := builder.First(&user).Error; err != nil {
		return false, msg, err
	}
	return true, i18n.Message{}, nil
}

func (s *userService) CheckEmailExist(db *gorm.DB, m *xmodel.User, email string) (bool, i18n.Message, error) {
	var err error
	msg := i18n.Message{
		ID:    "checker.user.email.does_not_exist",
		Other: "This email address is not registered.",
	}

	err = db.Where("email = ?", email).First(&m).Error
	if xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	}
	return true, msg, nil

}

// 檢查用戶郵箱是否唯一
func (s *userService) CheckUserEmailUnique(db *gorm.DB, email string, userId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.user.email.already_exists",
		Other: "Email already exists.",
	}
	var count int64
	builder := db.Model(&xmodel.User{}).Where("email = ?", email)
	if len(userId) > 0 {
		builder = builder.Where("id != ?", userId[0])
	}
	if err := builder.Count(&count).Error; err != nil {
		return false, msg, err
	}
	if count > 0 {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// 檢查用戶是否可以刪除
func (s *userService) CheckUserCanDelete(db *gorm.DB, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.user.id.cannot_delete",
		Other: "This user cannot be deleted.",
	}
	var user xmodel.User
	err := db.Model(&xmodel.User{}).Where("id = ?", userId).First(&user).Error
	if err != nil {
		return false, msg, err
	}
	// 機構主用戶不能刪除
	if user.UserType == model.UserUserTypeFacilityUser {
		var facilityUser model.FacilityUser
		err = db.Model(&model.FacilityUser{}).
			Where("user_id = ?", userId).
			Where("primary_user = ?", model.FacilityUserPrimaryUserY).
			First(&facilityUser).Error
		if xgorm.IsNotFoundErr(err) {
			return true, i18n.Message{}, nil
		} else if err != nil {
			return false, msg, err
		}
	}
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 用戶列表 ----------------------------------------------------

type UserListReq struct {
	UserType   string `form:"-"` // 本次請求的用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶, SUPER_ADMIN=超級管理員
	FacilityId uint64 `form:"-"` // 機構Id (當UserType=FACILITY_USER，代碼強制傳入)

	Name   string `form:"name" binding:"omitempty"`                        // 用戶名稱
	Email  string `form:"email" binding:"omitempty"`                       // 用戶郵箱
	RoleId uint64 `form:"roleId" binding:"omitempty"`                      // 角色
	Status string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
}

type UserListResp struct {
	UserId      uint64 `json:"userId"`
	Name        string `json:"name"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	RoleId      uint64 `json:"roleId"`
	RoleName    string `json:"roleName"`
	Status      string `json:"status"`
	PrimaryUser string `json:"primaryUser,omitempty"`
}

func (s *userService) List(db *gorm.DB, req UserListReq, pageSet *xresp.PageSet) ([]UserListResp, error) {
	var resp []UserListResp

	fields := []string{
		"u.id AS user_id",
		"u.name",
		"u.username",
		"u.email",
		"r.id AS role_id",
		"r.name AS role_name",
		"u.status",
	}
	builder := db.Table("user as u").
		Joins("JOIN user_role AS ur on ur.user_id = u.id").
		Joins("JOIN role AS r on r.id = ur.role_id")

	if req.UserType == model.UserUserTypeFacilityUser {
		builder = builder.Where("u.user_type = ?", req.UserType).
			Joins("JOIN facility_user AS fu on fu.user_id = u.id AND fu.facility_id = ?", req.FacilityId).
			Joins("LEFT JOIN facility_user_department AS fud ON fud.facility_user_id = u.id")
		fields = append(fields, "fu.primary_user", "fud.department_id")
	} else if req.UserType == model.UserUserTypeSystemAdmin || req.UserType == model.UserUserTypeSuperAdmin {
		builder = builder.Where("u.user_type = ?", model.UserUserTypeSystemAdmin)
	} else {
		return resp, nil
	}

	builder = builder.Select(fields)

	if req.Name != "" {
		builder = builder.Where("u.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Email != "" {
		builder = builder.Where("u.email LIKE ?", xgorm.EscapeLikeWithWildcards(req.Email))
	}
	if req.RoleId != 0 {
		builder = builder.Where("r.id = ?", req.RoleId)
	}
	if req.Status != "" {
		builder = builder.Where("status = ?", req.Status)
	}

	if err := builder.Scopes(xresp.Paginate(pageSet)).
		Order("u.id DESC").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 用戶列表 ----------------------------------------------------

// region ---------------------------------------------------- 用戶搜索 ----------------------------------------------------

type UserSearchReq struct {
	UserType   string `form:"-"`                                               // 本次請求的用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶, SUPER_ADMIN=超級管理員
	FacilityId uint64 `form:"-"`                                               // 機構Id (當UserType=FACILITY_USER，代碼強制傳入)
	Name       string `form:"name"`                                            // 用戶名稱
	Email      string `form:"email" `                                          // 用戶郵箱
	RoleId     uint64 `form:"roleId"`                                          // 角色
	Status     string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
	SelectedId uint64 `form:"selectedId"`                                      // 選中ID
	Limit      int    `form:"limit"`                                           // 每頁條目數
}

type UserSearchResp struct {
	UserId      uint64 `json:"userId"`
	Name        string `json:"name"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	RoleId      uint64 `json:"roleId"`
	RoleName    string `json:"roleName"`
	Status      string `json:"status"`
	PrimaryUser string `json:"primaryUser,omitempty"`
}

func (s *userService) Search(db *gorm.DB, req UserSearchReq) ([]UserSearchResp, error) {
	var resp []UserSearchResp

	fields := []string{
		"u.id AS user_id",
		"u.name",
		"u.username",
		"u.email",
		"r.id AS role_id",
		"r.name AS role_name",
		"u.status",
	}
	builder := db.Table("user as u").
		Joins("JOIN user_role AS ur on ur.user_id = u.id").
		Joins("JOIN role AS r on r.id = ur.role_id")

	if req.UserType == model.UserUserTypeFacilityUser {
		builder = builder.Where("u.user_type = ?", req.UserType).
			Joins("JOIN facility_user AS fu on fu.user_id = u.id AND fu.facility_id = ?", req.FacilityId).
			Joins("LEFT JOIN facility_user_department AS fud ON fud.facility_user_id = u.id")
		fields = append(fields, "fu.primary_user", "fud.department_id")
	} else if req.UserType == model.UserUserTypeSystemAdmin || req.UserType == model.UserUserTypeSuperAdmin {
		builder = builder.Where("u.user_type = ?", model.UserUserTypeSystemAdmin)
	} else {
		return resp, nil
	}

	builder = builder.Select(fields)

	if req.Name != "" {
		builder = builder.Where("u.name LIKE ?", xgorm.EscapeLikeWithWildcards(req.Name))
	}
	if req.Email != "" {
		builder = builder.Where("u.email LIKE ?", xgorm.EscapeLikeWithWildcards(req.Email))
	}
	if req.RoleId != 0 {
		builder = builder.Where("r.id = ?", req.RoleId)
	}
	if req.Status != "" {
		builder = builder.Where("status = ?", req.Status)
	}

	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(u.id = %d,0,1)", req.SelectedId))
	}

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	if err := builder.
		Order("u.id DESC").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 用戶列表 ----------------------------------------------------

// region ---------------------------------------------------- 用戶查詢 ----------------------------------------------------

type UserInquireReq struct {
	UserId     uint64 `form:"userId" binding:"required"`
	UserType   string `form:"-"` // 本次請求的用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶, SUPER_ADMIN=超級管理員
	FacilityId uint64 `form:"-"` // 機構Id (當UserType=FACILITY_USER，代碼強制傳入)
}

type UserInquireResp struct {
	UserId       uint64 `json:"userId"`                // 用戶ID
	DepartmentId uint64 `json:"departmentId"`          // 部門ID
	Name         string `json:"name"`                  // 名稱
	Email        string `json:"email"`                 // 電郵
	RoleId       uint64 `json:"roleId"`                // 角色ID
	Status       string `json:"status"`                // 狀態
	PrimaryUser  string `json:"primaryUser,omitempty"` // 主用戶
}

func (s *userService) Inquire(db *gorm.DB, req UserInquireReq) (UserInquireResp, error) {
	var err error
	var resp UserInquireResp
	var m xmodel.User
	builder := db.Where("id = ?", req.UserId)
	if req.UserType == model.UserUserTypeFacilityUser {
		builder = builder.Where("user_type = ?", req.UserType)
		var facilityUser model.FacilityUser
		if err = db.Where("user_id = ?", req.UserId).Where("facility_id = ?", req.FacilityId).First(&facilityUser).Error; err != nil {
			return resp, err
		}
		resp.PrimaryUser = facilityUser.PrimaryUser

		// 获取部门ID
		var facilityUserDepartment model.FacilityUserDepartment
		if err = db.Where("facility_user_id = ?", req.UserId).First(&facilityUserDepartment).Error; err != nil {
			if !xgorm.IsNotFoundErr(err) {
				return resp, err
			}
		} else {
			resp.DepartmentId = facilityUserDepartment.DepartmentId
		}
	} else if req.UserType == model.UserUserTypeSystemAdmin || req.UserType == model.UserUserTypeSuperAdmin {
		builder = builder.Where("user_type = ?", model.UserUserTypeSystemAdmin)
	} else {
		return resp, nil
	}
	if err = builder.First(&m).Error; err != nil {
		return resp, err
	}
	_ = copier.Copy(&resp, m)

	var userRole xmodel.UserRole
	if err = db.
		Where("user_id = ?", m.Id).
		First(&userRole).Error; err != nil {
		return resp, err
	}
	resp.RoleId = userRole.RoleId
	resp.UserId = m.Id
	return resp, nil
}

// endregion ---------------------------------------------------- 用戶查詢 ----------------------------------------------------

// region ---------------------------------------------------- 用戶創建 ----------------------------------------------------

type FacilityUserCreateReq struct {
	UserType     string `json:"-"`                                // 用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId   uint64 `json:"-"`                                // 機構Id (UserType=FACILITY_USER)
	DepartmentId uint64 `json:"departmentId"  binding:"required"` // 部門ID

	Name     string `json:"name" binding:"required"`                        // 名稱
	Email    string `json:"email" binding:"required,email"`                 // 電郵
	Password string `json:"password" binding:"required"`                    // 密碼
	RoleId   uint64 `json:"roleId" binding:"required"`                      // 角色Id
	Status   string `json:"status" binding:"required,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
}

type UserCreateReq struct {
	UserType     string `json:"-"` // 用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId   uint64 `json:"-"` // 機構Id (UserType=FACILITY_USER)
	DepartmentId uint64 `json:"-"` // 部門Id

	Name     string `json:"name" binding:"required"`                        // 名稱
	Email    string `json:"email" binding:"required,email"`                 // 電郵
	Password string `json:"password" binding:"required"`                    // 密碼
	RoleId   uint64 `json:"roleId" binding:"required"`                      // 角色Id
	Status   string `json:"status" binding:"required,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
}

type UserCreateResp struct {
	UserId uint64 `json:"userId"`
}

func (s *userService) Create(db *gorm.DB, req UserCreateReq) (UserCreateResp, error) {
	var err error
	var userCreateResp UserCreateResp
	var user xmodel.User
	_ = copier.Copy(&user, &req)
	user.Username = req.Email
	user.Password = xtool.EncodeStringWithSalt(req.Password, xconfig.AppConf.PasswordSalt)
	user.Status = xmodel.UserStatusEnable
	if err = db.Create(&user).Error; err != nil {
		return userCreateResp, err
	}
	userCreateResp.UserId = user.Id
	userRole := xmodel.UserRole{
		UserId: user.Id,
		RoleId: req.RoleId,
	}
	if err = db.Create(&userRole).Error; err != nil {
		return userCreateResp, err
	}
	if req.UserType == model.UserUserTypeFacilityUser {
		facilityUser := model.FacilityUser{
			FacilityId:  req.FacilityId,
			UserId:      user.Id,
			PrimaryUser: model.FacilityUserPrimaryUserN,
		}
		if err = db.Create(&facilityUser).Error; err != nil {
			return userCreateResp, err
		}

		// 创建机构用户部门关系
		facilityUserDepartment := model.FacilityUserDepartment{
			FacilityUserId: user.Id,
			DepartmentId:   req.DepartmentId,
		}
		if err = db.Create(&facilityUserDepartment).Error; err != nil {
			return userCreateResp, err
		}
	}
	return userCreateResp, nil
}

// endregion ---------------------------------------------------- 用戶創建 ----------------------------------------------------

// region ---------------------------------------------------- 用戶編輯 ----------------------------------------------------

type FacilityUserEditReq struct {
	UserType     string `json:"-"`                               // 用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId   uint64 `json:"-"`                               // 機構Id (UserType=FACILITY_USER)
	DepartmentId uint64 `json:"departmentId" binding:"required"` // 部門ID

	UserId   uint64 `json:"userId" binding:"required"`                      // 用戶Id
	Name     string `json:"name" binding:"required"`                        // 名稱
	Email    string `json:"email" binding:"required,email"`                 // 電郵
	Password string `json:"password" binding:"omitempty"`                   // 密碼
	RoleId   uint64 `json:"roleId" binding:"required"`                      // 角色Id
	Status   string `json:"status" binding:"required,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
}

type UserEditReq struct {
	UserType     string `json:"-"`                               // 用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId   uint64 `json:"-"`                               // 機構Id (UserType=FACILITY_USER)
	DepartmentId uint64 `json:"departmentId" binding:"required"` // 部門ID

	UserId   uint64 `json:"userId" binding:"required"`                      // 用戶Id
	Name     string `json:"name" binding:"required"`                        // 名稱
	Email    string `json:"email" binding:"required,email"`                 // 電郵
	Password string `json:"password" binding:"omitempty"`                   // 密碼
	RoleId   uint64 `json:"roleId" binding:"required"`                      // 角色Id
	Status   string `json:"status" binding:"required,oneof=ENABLE DISABLE"` // 狀態 ENABLE=啟用, DISABLE=禁用
}

func (s *userService) Edit(db *gorm.DB, req UserEditReq) error {
	var err error
	var user xmodel.User
	if err = db.Where("id = ?", req.UserId).Where("user_type = ?", req.UserType).First(&user).Error; err != nil {
		return err
	}
	user.Name = req.Name
	user.Email = req.Email
	user.Status = req.Status

	if req.Password != "" {
		user.Password = xtool.EncodeStringWithSalt(req.Password, xconfig.AppConf.PasswordSalt)
	}
	if err = db.Save(&user).Error; err != nil {
		return err
	}

	var userRole xmodel.UserRole
	if err = db.Where("user_id = ?", user.Id).First(&userRole).Error; err != nil {
		return err
	}
	roleChanged := userRole.RoleId != req.RoleId
	if roleChanged {
		userRole.RoleId = req.RoleId
		if err = db.Save(&userRole).Error; err != nil {
			return err
		}
		// 立即刪除緩存
		_ = xredis.DeleteKey(db.Statement.Context, fmt.Sprintf(xcasbin.RoleSubCacheKey, fmt.Sprintf("%d", user.Id)))
	}

	if req.UserType == model.UserUserTypeFacilityUser {
		// 查找对应的FacilityUserDepartment记录
		var facilityUserDepartment model.FacilityUserDepartment
		if err = db.Where("facility_user_id = ?", req.UserId).First(&facilityUserDepartment).Error; err != nil {
			if xgorm.IsNotFoundErr(err) {
				facilityUserDepartment = model.FacilityUserDepartment{
					FacilityUserId: req.UserId,
					DepartmentId:   req.DepartmentId,
				}
				if err = db.Create(&facilityUserDepartment).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		} else {
			facilityUserDepartment.DepartmentId = req.DepartmentId
			if err = db.Save(&facilityUserDepartment).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// endregion ---------------------------------------------------- 用戶編輯 ----------------------------------------------------

// region ---------------------------------------------------- 用戶刪除 ----------------------------------------------------

type UserDeleteReq struct {
	UserType   string `json:"-"` // 用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶
	FacilityId uint64 `json:"-"` // 機構Id (當 UserType 為 FACILITY_USER 時必填)

	UserId uint64 `json:"userId" binding:"required"` // 用戶Id (不能刪除機構主用戶)
}

func (s *userService) Delete(db *gorm.DB, req UserDeleteReq) error {
	var err error
	// 刪除機構用戶
	if req.UserType == model.UserUserTypeFacilityUser {
		if err = db.
			Where("facility_id = ?", req.FacilityId).
			Where("user_id = ?", req.UserId).
			Where("primary_user = ?", model.FacilityUserPrimaryUserN).
			Delete(&model.FacilityUser{}).Error; err != nil {
			return err
		}
		if err = db.Where("facility_user_id = ? ", req.UserId).Delete(&model.FacilityUserDepartment{}).Error; err != nil {
			return err
		}
	}
	// 刪除角色關聯
	if err = db.Where("user_id = ?", req.UserId).Delete(&xmodel.UserRole{}).Error; err != nil {
		return err
	}

	// 刪除Google用戶
	if err = db.Where("user_id = ?", req.UserId).Delete(&model.GoogleUser{}).Error; err != nil {
		return err
	}
	// 刪除用戶
	if err = db.Where("id = ?", req.UserId).Delete(&xmodel.User{}).Error; err != nil {
		return err
	}
	// 立即刪除緩存
	_ = xredis.DeleteKey(db.Statement.Context, fmt.Sprintf(xcasbin.RoleSubCacheKey, fmt.Sprintf("%d", req.UserId)))
	return nil
}

// endregion ---------------------------------------------------- 用戶刪除 ----------------------------------------------------

// 獲取用戶的名稱
func (s *userService) GetUserNickname(db *gorm.DB, userId uint64) (string, error) {
	var err error
	var name string
	var user xmodel.User
	if err = db.Where("id = ?", userId).First(&user).Error; err != nil {
		return "", err
	}
	switch user.UserType {
	case model.UserUserTypeSuperAdmin, model.UserUserTypeSystemAdmin:
		name = user.Name
	case model.UserUserTypeFacilityUser:
		err = db.Table("facility_user AS fu").
			Where("fu.user_id = ?", user.Id).
			Joins("JOIN facility_profile AS fp ON fp.facility_id = fu.facility_id AND fp.data_type = ?", model.FacilityProfileDataTypeDraft).
			Select("fp.name").
			Pluck("fp.name", &name).Error
		if err != nil {
			return "", err
		}
	case model.UserUserTypeProfessional:
		var professional model.Professional
		err = db.Model(&model.Professional{}).
			Where("user_id = ?", user.Id).
			Where("data_type = ?", model.ProfessionalDataTypeDraft).
			First(&professional).Error
		if err != nil {
			return "", err
		}
		name = fmt.Sprintf("%s %s", professional.FirstName, professional.LastName)
	}
	return name, nil
}

type UserSearchAllReq struct {
	UserType    string `form:"userType" binding:"omitempty,oneof=SYSTEM_ADMIN FACILITY_USER SUPER_ADMIN PROFESSIONAL"` // 本次請求的用戶類型 SYSTEM_ADMIN=系統管理員, FACILITY_USER=機構用戶, SUPER_ADMIN=超級管理員, PROFESSIONAL=專業人員
	NameOrEmail string `form:"nameOrEmail"`                                                                            // 用戶名稱或電郵
	RoleId      uint64 `form:"roleId"`                                                                                 // 角色
	Status      string `form:"status" binding:"omitempty,oneof=ENABLE DISABLE"`                                        // 狀態 ENABLE=啟用, DISABLE=禁用
	SelectedId  uint64 `form:"selectedId"`                                                                             // 選中ID
	Limit       int    `form:"limit"`                                                                                  // 每頁條目數
}

type UserSearchAllResp struct {
	UserId   uint64 `json:"userId"`
	UserType string `json:"userType"`
	Name     string `json:"name"`
	Username string `json:"username"`
	Email    string `json:"email"`
	RoleId   uint64 `json:"roleId"`
	RoleName string `json:"roleName"`
	Status   string `json:"status"`
}

func (s *userService) SearchAll(db *gorm.DB, req UserSearchAllReq) ([]UserSearchAllResp, error) {
	var resp []UserSearchAllResp

	builder := db.Table("user as u").
		Select([]string{
			"u.id AS user_id",
			"u.user_type",
			"u.name",
			"u.username",
			"u.email",
			"r.id AS role_id",
			"r.name AS role_name",
			"u.status",
		}).
		Joins("JOIN user_role AS ur on ur.user_id = u.id").
		Joins("JOIN role AS r on r.id = ur.role_id")
	if req.UserType != "" {
		builder = builder.Where("u.user_type = ?", req.UserType)
	}
	if req.NameOrEmail != "" {
		builder = builder.Where("u.name LIKE ? OR u.email LIKE ?", xgorm.EscapeLikeWithWildcards(req.NameOrEmail), xgorm.EscapeLikeWithWildcards(req.NameOrEmail))
	}
	if req.RoleId != 0 {
		builder = builder.Where("r.id = ?", req.RoleId)
	}
	if req.Status != "" {
		builder = builder.Where("status = ?", req.Status)
	}

	if req.SelectedId != 0 {
		builder = builder.Order(fmt.Sprintf("IF(u.id = %d,0,1)", req.SelectedId))
	}

	if req.Limit > 0 {
		builder = builder.Limit(req.Limit)
	}

	if err := builder.
		Order("u.id DESC").
		Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}
